<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.2.6.RELEASE</version>
  </parent>
  <groupId>com.mapfre.tron.gt</groupId>
  <artifactId>nwt_api_gt_be</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <packaging>pom</packaging>
  <name>${project.artifactId}:${project.version}</name>

  <modules>
    <module>1-DEVELOPMENT</module>
  </modules>
  <!--  Configuración SCM -->
  <scm>
    <developerConnection>${scm.developerConnection}</developerConnection>
    <url>${scm.url}</url>
    <connection>${scm.connection}</connection>
  </scm>
  <distributionManagement>
    <repository>
      <id>releases</id>
      <url>${releases.repo.url}</url>
    </repository>
    <snapshotRepository>
      <id>snapshots</id>
      <url>${snapshots.repo.url}</url>
    </snapshotRepository>
  </distributionManagement>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <java.version>1.8</java.version>
    <ojdbc-version>19.9.0.0</ojdbc-version>
    <!-- Context-Root -->
    <app.url.contextroot>/nwt_api_gt_be-web</app.url.contextroot>
    <!-- Propiedades de configuración SCM Plastic -->
    <!-- La siguiente propiedad define la ruta a la rama. Ejemplo /INTEGRATION, o /DEVELOPER -->
    <plastic.path.branch>TODO_RELLENAR</plastic.path.branch>
    <!-- La siguiente propiedad define el nombre del repositorio del código de la aplicación que da
    de alta el grupo de Gestión de Entornos -->
    <plastic.repo.name>TODO_RELLENAR</plastic.repo.name>
    <scm.username>TODO_RELLENAR</scm.username>
    <scm.password>TODO_RELLENAR</scm.password>
    <scm.developerConnection>
      scm:plasticscm:br:${plastic.path.branch}@${plastic.repo.name}@plastic:8087
    </scm.developerConnection>
    <scm.connection>scm:plasticscm:br:${plastic.path.branch}@${plastic.repo.name}@plastic:8087</scm.connection>
    <scm.url>scm:plasticscm:br:${plastic.path.branch}@${plastic.repo.name}@plastic:8087</scm.url>
    <!-- Fin propiedades de configuración SCM Plastic -->
    <resource.delimiter>${*}</resource.delimiter>
    <gaia-boot-dependencies.version>1.1.0</gaia-boot-dependencies.version>
    <GAIA_VERSION>3.1.1</GAIA_VERSION>
    <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
    <logbook.version>3.8.0</logbook.version>
    <!-- Propiedades de configuracion PDA -->
    <pda.type>GAIA</pda.type>
    <!-- API Client versions-->
    <NWT_CMN_API_BE-CLIENT_VERSION>25.02.05-SNAPSHOT</NWT_CMN_API_BE-CLIENT_VERSION>
    <NWT_BTC_API_BE-CLIENT_VERSION>25.02.05-SNAPSHOT</NWT_BTC_API_BE-CLIENT_VERSION>
    <NWT_ISU_API_BE-CLIENT_VERSION>25.02.06-SNAPSHOT</NWT_ISU_API_BE-CLIENT_VERSION>
    <NWT_LSS_API_BE-CLIENT_VERSION>25.02.05-SNAPSHOT</NWT_LSS_API_BE-CLIENT_VERSION>
    <NWT_SPL_API_BE-CLIENT_VERSION>25.02.05-SNAPSHOT</NWT_SPL_API_BE-CLIENT_VERSION>
    <NWT_THP_API_BE-CLIENT_VERSION>25.02.05-SNAPSHOT</NWT_THP_API_BE-CLIENT_VERSION>
    <NWT_TSY_API_BE-CLIENT_VERSION>25.02.05-SNAPSHOT</NWT_TSY_API_BE-CLIENT_VERSION>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.mapfre.dgtp.gaia-boot</groupId>
        <artifactId>gaia-boot-dependencies</artifactId>
        <version>${gaia-boot-dependencies.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc</groupId>
        <artifactId>ojdbc8</artifactId>
        <version>${ojdbc-version}</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.nls</groupId>
        <artifactId>orai18n</artifactId>
        <version>${ojdbc-version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.jackson</groupId>
        <artifactId>jackson-mapper-asl</artifactId>
        <version>1.9.5</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.jackson</groupId>
        <artifactId>jackson-core-asl</artifactId>
        <version>1.9.5</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>org.junit.vintage</groupId>
          <artifactId>junit-vintage-engine</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mapfre.dgtp.gaia-boot</groupId>
      <artifactId>gaia-boot-actuator</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mapfre.dgtp.gaia-boot</groupId>
      <artifactId>gaia-boot-backend-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mapfre.dgtp.gaia-boot</groupId>
      <artifactId>gaia-boot-starter-security</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-security</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
    <dependency>
      <groupId>io.springfox</groupId>
      <artifactId>springfox-swagger2</artifactId>
    </dependency>
    <dependency>
      <groupId>io.springfox</groupId>
      <artifactId>springfox-swagger-ui</artifactId>
    </dependency>
    <dependency>
      <groupId>io.springfox</groupId>
      <artifactId>springfox-boot-starter</artifactId>
      <version>3.0.0</version>
    </dependency>
    <dependency>
      <groupId>com.mapfre.dgtp.gaia</groupId>
      <artifactId>gaia-mapper</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mapfre.dgtp.gaia</groupId>
      <artifactId>gaia-httpinvoker-server</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mapfre.dgtp.gaia-boot</groupId>
      <artifactId>gaia-boot-plinvoker-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mapfre.dgtp.plinvoker</groupId>
      <artifactId>plinvoker</artifactId>
      <version>1.1.7</version>
    </dependency>
    <dependency>
      <groupId>com.mapfre.dgtp.gaia</groupId>
      <artifactId>gaia-plinvoker</artifactId>
      <version>3.4.2</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-jdbc</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
      <version>${org.mapstruct.version}</version>
    </dependency>
    <dependency>
      <groupId>org.zalando</groupId>
      <artifactId>logbook-spring</artifactId>
      <version>${logbook.version}</version>
    </dependency>
  </dependencies>

  <build>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>true</filtering>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-scm-plugin</artifactId>
        <version>1.11.2</version>
        <dependencies>
          <dependency>
            <groupId>com.codicesoftware.plastic.maven</groupId>
            <artifactId>maven-scm-provider-plasticscm</artifactId>
            <version>1.8.1</version>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <artifactId>maven-release-plugin</artifactId>
        <version>2.5.3</version>
        <configuration>
          <username>${scm.username}</username>
          <password>${scm.password}</password>
          <commitByProject>false</commitByProject>
          <preparationGoals>clean</preparationGoals>
          <goals>clean</goals>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>org.apache.maven.scm</groupId>
            <artifactId>maven-scm-api</artifactId>
            <version>1.11.2</version>
          </dependency>
          <dependency>
            <groupId>com.codicesoftware.plastic.maven</groupId>
            <artifactId>maven-scm-provider-plasticscm</artifactId>
            <version>1.8.1</version>
          </dependency>
        </dependencies>
      </plugin>
    </plugins>
  </build>
  <reporting>
    <plugins>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <inherited>true</inherited>
        <configuration>
          <aggregate>false</aggregate>
          <excludePackageNames>org.owasp.*</excludePackageNames>
        </configuration>
      </plugin>
    </plugins>
  </reporting>
</project>