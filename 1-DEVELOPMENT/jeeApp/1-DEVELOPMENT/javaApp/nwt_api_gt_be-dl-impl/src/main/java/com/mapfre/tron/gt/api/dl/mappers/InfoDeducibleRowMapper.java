package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.InfoDeducibleResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos InfoDeducibleResponse.
 */
@Slf4j
public class InfoDeducibleRowMapper implements RowMapper<InfoDeducibleResponse> {

    @Override
    public InfoDeducibleResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto InfoDeducibleResponse", rowNum);
        
        InfoDeducibleResponse response = new InfoDeducibleResponse();
        
        try {
            response.setNumLiq(rs.getString("numLiq"));
            response.setNumSini(rs.getString("numSini"));
            response.setNumExp(rs.getInt("numExp"));
            response.setNumPoliza(rs.getString("numPoliza"));
            response.setCodNivel3(rs.getString("codNivel3"));
            response.setNomNivel3(rs.getString("nomNivel3"));
            response.setCodRamo(rs.getInt("codRamo"));
            response.setCodSector(rs.getInt("codSector"));
            response.setNomSector(rs.getString("nomSector"));
            response.setCodTercero(rs.getString("codTercero"));
            response.setNomTercero(rs.getString("nomTercero"));
            response.setCodActTercero(rs.getInt("codActTercero"));
            response.setNomActTercero(rs.getString("nomActTercero"));
            response.setTipDocum(rs.getString("tipDocum"));
            response.setCodDocum(rs.getString("codDocum"));
            response.setObs(rs.getString("obs"));
            response.setFecLiq(rs.getString("fecLiq"));
            response.setFecEstPago(rs.getString("fecEstPago"));
            response.setFecPago(rs.getString("fecPago"));
            response.setCodMonLiq(rs.getInt("codMonLiq"));
            response.setCodMonLiqIso(rs.getString("codMonLiqIso"));
            response.setNumDecimales(rs.getInt("numDecimales"));
            response.setCodMonPago(rs.getInt("codMonPago"));
            response.setCodMonPagoIso(rs.getString("codMonPagoIso"));
            response.setImpLiqNeto(rs.getDouble("impLiqNeto"));
            response.setImpIva(rs.getDouble("impIva"));
            response.setImpLiq(rs.getDouble("impLiq"));
            response.setValCambio(rs.getDouble("valCambio"));
            response.setTipDocto(rs.getString("tipDocto"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return response;
    }
}
