package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.AvisoPagoPolizaGrupoResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos AvisoPagoPolizaGrupoResponse.
 */
@Slf4j
public class AvisoPagoPolizaGrupoRowMapper implements RowMapper<AvisoPagoPolizaGrupoResponse> {

    @Override
    public AvisoPagoPolizaGrupoResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto AvisoPagoPolizaGrupoResponse", rowNum);
        
        AvisoPagoPolizaGrupoResponse response = new AvisoPagoPolizaGrupoResponse();
        
        try {
            response.setCodActTercero(rs.getInt("codActTercero"));
            response.setCodAgt(rs.getString("codAgt"));
            response.setCodCia(rs.getInt("codCia"));
            response.setCodDocum(rs.getString("codDocum"));
            response.setCodDocumPago(rs.getString("codDocumPago"));
            response.setCodGestor(rs.getInt("codGestor"));
            response.setCodMon(rs.getInt("codMon"));
            response.setFecMvto(rs.getString("fecMvto"));
            response.setFecVcto(rs.getString("fecVcto"));
            response.setImpDocum(rs.getDouble("impDocum"));
            response.setNumContrato(rs.getInt("numContrato"));
            response.setNumMvto(rs.getInt("numMvto"));
            response.setNumPoliza(rs.getString("numPoliza"));
            response.setNumPolizaCliente(rs.getString("numPolizaCliente"));
            response.setNumPolizaGrupo(rs.getString("numPolizaGrupo"));
            response.setTipDocum(rs.getString("tipDocum"));
            response.setTipDocumPago(rs.getString("tipDocumPago"));
            response.setTipEstado(rs.getString("tipEstado"));
            response.setTipGestor(rs.getString("tipGestor"));
            response.setValCambio(rs.getDouble("valCambio"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return response;
    }
}
