<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.mapfre.tron.gt</groupId>
		<artifactId>nwt_api_gt_be.jeeApp</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>

	<artifactId>nwt_api_gt_be-web</artifactId>
	<packaging>war</packaging>

	<name>${project.artifactId}:${project.version}</name>
	<description>${project.artifactId}:${project.version}</description>

	<properties>
		<WAR_CONTEXTO>/nwt_api_gt_be-web</WAR_CONTEXTO>
		<start-class>com.mapfre.tron.gt.api.NwtApiGtBeApplication</start-class>
		<directoryExcludesJars>**/*.jar</directoryExcludesJars>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.mapfre.tron.gt</groupId>
			<artifactId>nwt_api_gt_be-config</artifactId>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.mapfre.tron.gt</groupId>
			<artifactId>nwt_api_gt_be-web-dependencies</artifactId>
			<type>pom</type>
		</dependency>
		<!-- Required to included tomcat dependency in lib-provided -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-tomcat</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.mapfre.tron.gt</groupId>
			<artifactId>nwt_api_gt_be.zeroConfig</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.mapfre.dgtp.gaia</groupId>
			<artifactId>MAPFRE_GAIA_ENVCONFIG_DIST</artifactId>
			<scope>provided</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<classifier>exec</classifier>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<version>3.2.3</version>
				<executions>
					<execution>
						<id>onlywar</id>
						<phase>package</phase>
						<goals>
							<goal>war</goal>
						</goals>
						<configuration>
							<!-- Se excluyen las librerias del WEB-INF/lib -->
							<failOnMissingWebXml>false</failOnMissingWebXml>
							<archive>
								<manifest>
									<mainClass>${start-class}</mainClass>
									<addDefaultImplementationEntries>true</addDefaultImplementationEntries>
									<addClasspath>true</addClasspath>
								</manifest>
							</archive>
							<attachClasses>true</attachClasses>
							<webResources>
								<resource>
									<filtering>true</filtering>
									<directory>src/main/webapp</directory>
									<includes>
										<include>**/weblogic.xml</include>
									</includes>
									<targetPath>.</targetPath>
								</resource>
							</webResources>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>com.spotify</groupId>
				<artifactId>dockerfile-maven-plugin</artifactId>
				<version>1.4.13</version>
				<executions>
					<execution>
						<id>default</id>
						<!--<goals> <goal>build</goal> </goals> -->
					</execution>
				</executions>
				<configuration>
					<verbose>true</verbose>
					<repository>mapfre/${project.artifactId}</repository>
					<tag>${project.version}</tag>
					<buildDirectory>target</buildDirectory>
					<dockerfile>./docker/Dockerfile</dockerfile>
					<buildArgs>
						<JAR_FILE>${project.build.finalName}-exec.war</JAR_FILE>
					</buildArgs>
				</configuration>
			</plugin>
		</plugins>
	</build>
	<profiles>
		<profile>
			<id>INCLUDE_DEPENDENCIES</id>
			<properties>
				<!-- Define LOCAL environment properties here -->
				<!-- This property defines the active Spring profile. -->
				<app.env.spring.active.profile>ENV_LOCAL</app.env.spring.active.profile>
			</properties>
			<dependencies>
				<dependency>
					<groupId>com.mapfre.tron.gt</groupId>
					<artifactId>nwt_api_gt_be.zeroConfig</artifactId>
					<scope>compile</scope>
				</dependency>
				<dependency>
					<groupId>com.mapfre.dgtp.gaia</groupId>
					<artifactId>MAPFRE_GAIA_ENVCONFIG_DIST</artifactId>
					<scope>compile</scope>
				</dependency>
			</dependencies>
		</profile>
		
	</profiles>
</project>
