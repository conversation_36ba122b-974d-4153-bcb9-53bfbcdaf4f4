<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.mapfre.tron.gt</groupId>
        <artifactId>nwt_api_gt_be.DEVELOPMENT</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>nwt_api_gt_be.zeroConfig</artifactId>
    <packaging>jar</packaging>
    <name>${project.artifactId}:${project.version}</name>

    <properties>
        <!-- This properties define application info properties -->
        <app.system.info.project.version>${project.version}</app.system.info.project.version>
        <!--Class loader in Was 7/8.5 must be PARENT_LAST-->
        <app.classloader.mode>PARENT_LAST</app.classloader.mode>
        <app.env.server.type>weblogic</app.env.server.type>
        <!--This property set if GaiaRestExceptionHandler is enabled, for handler exception and transform to GaiaSoaException -->
        <gaia.env.rest.exceptionHanlder.enabled>true</gaia.env.rest.exceptionHanlder.enabled>
        <!--This property defines the number of StackTrace that are shown in a GaiaSoaExceptionInfo -->
        <app.env.soa.error.max-stack-trace-elements>5</app.env.soa.error.max-stack-trace-elements>
        <!--This property defines the application name that is shown in GaiaSoaExceptionInfo -->
        <app.env.name>nwt_api_gt_be-web</app.env.name>
        <!-- Cache - Cleans caches every day at 02:00 -->
		<cache.clean.cron.expression>0 0 2 * * ?</cache.clean.cron.expression>
        <!-- Datasource -->
        <app.env.dl.datasource.ref.jndi>jdbc/gaia_tron2000_hn_app</app.env.dl.datasource.ref.jndi>
        <app.env.dl.datasource.url></app.env.dl.datasource.url>
        <app.env.dl.datasource.u></app.env.dl.datasource.u>
        <app.env.dl.datasource.p></app.env.dl.datasource.p>
        <!-- Documentum -->
        <app.env.documentum.repository>DMDESACA</app.env.documentum.repository>
        <app.env.documentum.profile>profile.HONDURAS</app.env.documentum.profile>
        <app.env.documentum.documentType>[{"secVal":3,"dcnTyp":"mphnd_do_policy"},{"secVal":4,"dcnTyp":"mphnd_do_lifepolicy"},{"secVal":7,"dcnTyp":"mphnd_do_lifepolicy"},{"secVal":2,"dcnTyp":"mphnd_do_propertypolicy"},{"secVal":5,"dcnTyp":"mphnd_do_propertypolicy"},{"secVal":6,"dcnTyp":"mphnd_do_healthpolicy"}]</app.env.documentum.documentType>

<!--        BANGUAT-->
        <app.env.soap.banguat.endpoint>https://banguat.gob.gt/variables/ws/TipoCambio.asmx</app.env.soap.banguat.endpoint>
        <app.env.soap.banguat.action>http://www.banguat.gob.gt/variables/ws/TipoCambioDia</app.env.soap.banguat.action>

    </properties>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>**/*.txt</exclude>
                </excludes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.2.3</version>
                <executions>
                    <execution>
                        <id>wl-artifact</id>
                        <phase>package</phase>
                        <goals>
                            <goal>war</goal>
                        </goals>
                        <configuration>
                            <properties>
                                <pack>war</pack>
                            </properties>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <packagingExcludes>WEB-INF/lib/**</packagingExcludes>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <archive>
                        <manifest>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>

                        </manifest>
                        <manifestEntries>
                            <Specification-Version>1.0.0</Specification-Version>
                            <Implementation-Version>1.0.0</Implementation-Version>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>ENV_LOCAL</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <!-- Define LOCAL environment properties here -->
                <!-- This property defines the active Spring profile. -->
                <app.env.spring.active.profile>real with direct-connection and demo-security</app.env.spring.active.profile>
                <!-- This property defines the server where the applicaction is going to be
                deployed. It is used to obtain the data base connection. -->
                <app.env.server.type>tomcat</app.env.server.type>
                <!-- Datasource -->
                <app.env.dl.datasource.ref.jndi></app.env.dl.datasource.ref.jndi>
                <app.env.dl.datasource.url>****************************** = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = ***********)(PORT = 1521))) (CONNECT_DATA = (SERVICE_NAME = pdbwpsp4_pan_des.databasepanamad.vcndestronpanam.oraclevcn.com)))</app.env.dl.datasource.url>
                <app.env.dl.datasource.u>TRON2000_GT_APP</app.env.dl.datasource.u>
                <app.env.dl.datasource.p>rKmKtPtZ5TPU3eA1tjTf</app.env.dl.datasource.p>
                
                <!-- API Clients -->
                <app.env.tron.api.cmn.basePath>https://tron-desa-ca.reef.mapfre.net/nwt_cmn_api_be-web/newtron/api/common</app.env.tron.api.cmn.basePath>
                <app.env.tron.api.cmn.userName>APITRON</app.env.tron.api.cmn.userName>
                <app.env.tron.api.cmn.password>Mapfre2019</app.env.tron.api.cmn.password>
                <app.env.tron.api.btc.basePath>https://tron-desa-ca.reef.mapfre.net/nwt_btc_api_be-web/newtron/api/common</app.env.tron.api.btc.basePath> 
				<app.env.tron.api.btc.userName>APITRON</app.env.tron.api.btc.userName>
				<app.env.tron.api.btc.password>Mapfre2019</app.env.tron.api.btc.password> 
                <app.env.tron.api.isu.basePath>https://tron-desa-ca.reef.mapfre.net/nwt_isu_api_be-web/newtron/api/issue</app.env.tron.api.isu.basePath>
                <app.env.tron.api.isu.userName>APITRON</app.env.tron.api.isu.userName>
                <app.env.tron.api.isu.password>Mapfre2019</app.env.tron.api.isu.password>
                <app.env.tron.api.lss.basePath>https://tron-desa-ca.reef.mapfre.net/nwt_lss_api_be-web/newtron/api/loss</app.env.tron.api.lss.basePath>
				<app.env.tron.api.lss.userName>APITRON</app.env.tron.api.lss.userName>
				<app.env.tron.api.lss.password>Mapfre2019</app.env.tron.api.lss.password>
                <app.env.tron.api.spl.basePath>https://tron-desa-ca.reef.mapfre.net/nwt_spl_api_be-web/newtron/api/supplier</app.env.tron.api.spl.basePath>
				<app.env.tron.api.spl.userName>APITRON</app.env.tron.api.spl.userName>
				<app.env.tron.api.spl.password>Mapfre2019</app.env.tron.api.spl.password>
                <app.env.tron.api.thp.basePath>https://tron-desa-ca.reef.mapfre.net/nwt_thp_api_be-web/newtron/api/thirdparty</app.env.tron.api.thp.basePath>
                <app.env.tron.api.thp.userName>APITRON</app.env.tron.api.thp.userName>
                <app.env.tron.api.thp.password>Mapfre2019</app.env.tron.api.thp.password>
                <app.env.tron.api.tsy.basePath>https://tron-desa-ca.reef.mapfre.net/nwt_tsy_api_be-web/newtron/api/treasury</app.env.tron.api.tsy.basePath>
                <app.env.tron.api.tsy.userName>APITRON</app.env.tron.api.tsy.userName>
                <app.env.tron.api.tsy.password>Mapfre2019</app.env.tron.api.tsy.password>
                <!-- Report API -->
                <app.env.tron.api.report.basePath>https://tron-desa-ca.reef.mapfre.net/report_be-web/api/reports</app.env.tron.api.report.basePath>
                
            </properties>
        </profile>
        <profile>
            <id>ENV_IC</id>
            <properties>
                <!-- This property defines the active Spring profile. -->
                <app.env.spring.active.profile>real with demo-security</app.env.spring.active.profile>

                <!-- API Clients -->
                <app.env.tron.api.cmn.basePath>https://tron-ic-ca.reef.mapfre.net/nwt_cmn_api_be-web/newtron/api/common</app.env.tron.api.cmn.basePath>
                <app.env.tron.api.cmn.userName>APITRON</app.env.tron.api.cmn.userName>
                <app.env.tron.api.cmn.password>Mapfre2019</app.env.tron.api.cmn.password>
                <app.env.tron.api.btc.basePath>https://tron-ic-ca.reef.mapfre.net/nwt_btc_api_be-web/newtron/api/common</app.env.tron.api.btc.basePath> 
				<app.env.tron.api.btc.userName>APITRON</app.env.tron.api.btc.userName>
				<app.env.tron.api.btc.password>Mapfre2019</app.env.tron.api.btc.password> 
                <app.env.tron.api.isu.basePath>https://tron-ic-ca.reef.mapfre.net/nwt_isu_api_be-web/newtron/api/issue</app.env.tron.api.isu.basePath>
                <app.env.tron.api.isu.userName>APITRON</app.env.tron.api.isu.userName>
                <app.env.tron.api.isu.password>Mapfre2019</app.env.tron.api.isu.password>
                <app.env.tron.api.lss.basePath>https://tron-ic-ca.reef.mapfre.net/nwt_lss_api_be-web/newtron/api/loss</app.env.tron.api.lss.basePath>
				<app.env.tron.api.lss.userName>APITRON</app.env.tron.api.lss.userName>
				<app.env.tron.api.lss.password>Mapfre2019</app.env.tron.api.lss.password>
                <app.env.tron.api.spl.basePath>https://tron-ic-ca.reef.mapfre.net/nwt_spl_api_be-web/newtron/api/supplier</app.env.tron.api.spl.basePath>
				<app.env.tron.api.spl.userName>APITRON</app.env.tron.api.spl.userName>
				<app.env.tron.api.spl.password>Mapfre2019</app.env.tron.api.spl.password>
                <app.env.tron.api.thp.basePath>https://tron-ic-ca.reef.mapfre.net/nwt_thp_api_be-web/newtron/api/thirdparty</app.env.tron.api.thp.basePath>
                <app.env.tron.api.thp.userName>APITRON</app.env.tron.api.thp.userName>
                <app.env.tron.api.thp.password>Mapfre2019</app.env.tron.api.thp.password>
                <app.env.tron.api.tsy.basePath>https://tron-ic-ca.reef.mapfre.net/nwt_tsy_api_be-web/newtron/api/treasury</app.env.tron.api.tsy.basePath>
                <app.env.tron.api.tsy.userName>APITRON</app.env.tron.api.tsy.userName>
                <app.env.tron.api.tsy.password>Mapfre2019</app.env.tron.api.tsy.password>
                <!-- Report API -->
                <app.env.tron.api.report.basePath>https://tron-ic-ca.reef.mapfre.net/report_be-web/api/reports</app.env.tron.api.report.basePath>

            </properties>
        </profile>
        <profile>
            <id>ENV_INT</id>
            <properties>
                <!-- This property defines the active Spring profile. -->
                <app.env.spring.active.profile>real with demo-security</app.env.spring.active.profile>

                <!-- API Clients -->
                <app.env.tron.api.cmn.basePath>https://tron-int-ca.reef.mapfre.net/nwt_cmn_api_be-web/newtron/api/common</app.env.tron.api.cmn.basePath>
                <app.env.tron.api.cmn.userName>APITRON</app.env.tron.api.cmn.userName>
                <app.env.tron.api.cmn.password>Mapfre2019</app.env.tron.api.cmn.password>
                <app.env.tron.api.btc.basePath>https://tron-int-ca.reef.mapfre.net/nwt_btc_api_be-web/newtron/api/common</app.env.tron.api.btc.basePath> 
				<app.env.tron.api.btc.userName>APITRON</app.env.tron.api.btc.userName>
				<app.env.tron.api.btc.password>Mapfre2019</app.env.tron.api.btc.password> 
                <app.env.tron.api.isu.basePath>https://tron-int-ca.reef.mapfre.net/nwt_isu_api_be-web/newtron/api/issue</app.env.tron.api.isu.basePath>
                <app.env.tron.api.isu.userName>APITRON</app.env.tron.api.isu.userName>
                <app.env.tron.api.isu.password>Mapfre2019</app.env.tron.api.isu.password>
                <app.env.tron.api.lss.basePath>https://tron-int-ca.reef.mapfre.net/nwt_lss_api_be-web/newtron/api/loss</app.env.tron.api.lss.basePath>
				<app.env.tron.api.lss.userName>APITRON</app.env.tron.api.lss.userName>
				<app.env.tron.api.lss.password>Mapfre2019</app.env.tron.api.lss.password>
                <app.env.tron.api.spl.basePath>https://tron-int-ca.reef.mapfre.net/nwt_spl_api_be-web/newtron/api/supplier</app.env.tron.api.spl.basePath>
				<app.env.tron.api.spl.userName>APITRON</app.env.tron.api.spl.userName>
				<app.env.tron.api.spl.password>Mapfre2019</app.env.tron.api.spl.password>
                <app.env.tron.api.thp.basePath>https://tron-int-ca.reef.mapfre.net/nwt_thp_api_be-web/newtron/api/thirdparty</app.env.tron.api.thp.basePath>
                <app.env.tron.api.thp.userName>APITRON</app.env.tron.api.thp.userName>
                <app.env.tron.api.thp.password>Mapfre2019</app.env.tron.api.thp.password>
                <app.env.tron.api.tsy.basePath>https://tron-int-ca.reef.mapfre.net/nwt_tsy_api_be-web/newtron/api/treasury</app.env.tron.api.tsy.basePath>
                <app.env.tron.api.tsy.userName>APITRON</app.env.tron.api.tsy.userName>
                <app.env.tron.api.tsy.password>Mapfre2019</app.env.tron.api.tsy.password>
                <!-- Report API -->
                <app.env.tron.api.report.basePath>https://tron-int-ca.reef.mapfre.net/report_be-web/api/reports</app.env.tron.api.report.basePath>

            </properties>
        </profile>
        <profile>
            <id>ENV_PRE</id>
            <properties>
                <!-- This property defines the active Spring profile. -->
                <app.env.spring.active.profile>real with demo-security</app.env.spring.active.profile>

                <!-- API Clients -->
                <app.env.tron.api.cmn.basePath>https://tron-pre-ca.reef.mapfre.net/nwt_cmn_api_be-web/newtron/api/common</app.env.tron.api.cmn.basePath>
                <app.env.tron.api.cmn.userName>APITRON</app.env.tron.api.cmn.userName>
                <app.env.tron.api.cmn.password>Mapfre2019</app.env.tron.api.cmn.password>
                <app.env.tron.api.btc.basePath>https://tron-pre-ca.reef.mapfre.net/nwt_btc_api_be-web/newtron/api/common</app.env.tron.api.btc.basePath> 
				<app.env.tron.api.btc.userName>APITRON</app.env.tron.api.btc.userName>
				<app.env.tron.api.btc.password>Mapfre2019</app.env.tron.api.btc.password> 
                <app.env.tron.api.isu.basePath>https://tron-pre-ca.reef.mapfre.net/nwt_isu_api_be-web/newtron/api/issue</app.env.tron.api.isu.basePath>
                <app.env.tron.api.isu.userName>APITRON</app.env.tron.api.isu.userName>
                <app.env.tron.api.isu.password>Mapfre2019</app.env.tron.api.isu.password>
                <app.env.tron.api.lss.basePath>https://tron-pre-ca.reef.mapfre.net/nwt_lss_api_be-web/newtron/api/loss</app.env.tron.api.lss.basePath>
				<app.env.tron.api.lss.userName>APITRON</app.env.tron.api.lss.userName>
				<app.env.tron.api.lss.password>Mapfre2019</app.env.tron.api.lss.password>
                <app.env.tron.api.spl.basePath>https://tron-pre-ca.reef.mapfre.net/nwt_spl_api_be-web/newtron/api/supplier</app.env.tron.api.spl.basePath>
				<app.env.tron.api.spl.userName>APITRON</app.env.tron.api.spl.userName>
				<app.env.tron.api.spl.password>Mapfre2019</app.env.tron.api.spl.password>
                <app.env.tron.api.thp.basePath>https://tron-pre-ca.reef.mapfre.net/nwt_thp_api_be-web/newtron/api/thirdparty</app.env.tron.api.thp.basePath>
                <app.env.tron.api.thp.userName>APITRON</app.env.tron.api.thp.userName>
                <app.env.tron.api.thp.password>Mapfre2019</app.env.tron.api.thp.password>
                <app.env.tron.api.tsy.basePath>https://tron-pre-ca.reef.mapfre.net/nwt_tsy_api_be-web/newtron/api/treasury</app.env.tron.api.tsy.basePath>
                <app.env.tron.api.tsy.userName>APITRON</app.env.tron.api.tsy.userName>
                <app.env.tron.api.tsy.password>Mapfre2019</app.env.tron.api.tsy.password>
                <!-- Report API -->
                <app.env.tron.api.report.basePath>https://tron-pre-ca.reef.mapfre.net/report_be-web/api/reports</app.env.tron.api.report.basePath>

            </properties>
        </profile>
        <profile>
            <id>ENV_PRO</id>
            <properties>
                <!-- This property defines the active Spring profile. -->
                <app.env.spring.active.profile>real with demo-security</app.env.spring.active.profile>

                <!-- API Clients -->
                <!-- API Clients -->
                <app.env.tron.api.cmn.basePath>https://tron-ca.reef.mapfre.net/nwt_cmn_api_be-web/newtron/api/common</app.env.tron.api.cmn.basePath>
                <app.env.tron.api.cmn.userName>APITRON</app.env.tron.api.cmn.userName>
                <app.env.tron.api.cmn.password>Mapfre2019</app.env.tron.api.cmn.password>
                <app.env.tron.api.btc.basePath>https://tron-ca.reef.mapfre.net/nwt_btc_api_be-web/newtron/api/common</app.env.tron.api.btc.basePath> 
				<app.env.tron.api.btc.userName>APITRON</app.env.tron.api.btc.userName>
				<app.env.tron.api.btc.password>Mapfre2019</app.env.tron.api.btc.password> 
                <app.env.tron.api.isu.basePath>https://tron-ca.reef.mapfre.net/nwt_isu_api_be-web/newtron/api/issue</app.env.tron.api.isu.basePath>
                <app.env.tron.api.isu.userName>APITRON</app.env.tron.api.isu.userName>
                <app.env.tron.api.isu.password>Mapfre2019</app.env.tron.api.isu.password>
                <app.env.tron.api.lss.basePath>https://tron-ca.reef.mapfre.net/nwt_lss_api_be-web/newtron/api/loss</app.env.tron.api.lss.basePath>
				<app.env.tron.api.lss.userName>APITRON</app.env.tron.api.lss.userName>
				<app.env.tron.api.lss.password>Mapfre2019</app.env.tron.api.lss.password>
                <app.env.tron.api.spl.basePath>https://tron-ca.reef.mapfre.net/nwt_spl_api_be-web/newtron/api/supplier</app.env.tron.api.spl.basePath>
				<app.env.tron.api.spl.userName>APITRON</app.env.tron.api.spl.userName>
				<app.env.tron.api.spl.password>Mapfre2019</app.env.tron.api.spl.password>
                <app.env.tron.api.thp.basePath>https://tron-ca.reef.mapfre.net/nwt_thp_api_be-web/newtron/api/thirdparty</app.env.tron.api.thp.basePath>
                <app.env.tron.api.thp.userName>APITRON</app.env.tron.api.thp.userName>
                <app.env.tron.api.thp.password>Mapfre2019</app.env.tron.api.thp.password>
                <app.env.tron.api.tsy.basePath>https://tron-ca.reef.mapfre.net/nwt_tsy_api_be-web/newtron/api/treasury</app.env.tron.api.tsy.basePath>
                <app.env.tron.api.tsy.userName>APITRON</app.env.tron.api.tsy.userName>
                <app.env.tron.api.tsy.password>Mapfre2019</app.env.tron.api.tsy.password>
                <!-- Report API -->
                <app.env.tron.api.report.basePath>https://tron-ca.reef.mapfre.net/report_be-web/api/reports</app.env.tron.api.report.basePath>

                <!-- Documentum -->
                <app.env.documentum.repository>DMPRODCA</app.env.documentum.repository>

            </properties>
        </profile>
    </profiles>
</project>