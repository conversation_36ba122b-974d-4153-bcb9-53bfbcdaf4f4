package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ResponsablePagoResponse
 */
@Validated

public class ResponsablePagoResponse   {
  @JsonProperty("codCia")
  private Integer codCia = null;

  @JsonProperty("codActTercero")
  private Integer codActTercero = null;

  @JsonProperty("tipDocum")
  private String tipDocum = null;

  @JsonProperty("codDocum")
  private String codDocum = null;

  @JsonProperty("nombreCompleto")
  private String nombreCompleto = null;

  @JsonProperty("direcCobro")
  private String direcCobro = null;

  @JsonProperty("email")
  private String email = null;

  // Getters and Setters
  public ResponsablePagoResponse codCia(Integer codCia) {
    this.codCia = codCia;
    return this;
  }

  @ApiModelProperty(example = "5", value = "Código de compañía")
  public Integer getCodCia() {
    return codCia;
  }

  public void setCodCia(Integer codCia) {
    this.codCia = codCia;
  }

  public ResponsablePagoResponse codActTercero(Integer codActTercero) {
    this.codActTercero = codActTercero;
    return this;
  }

  @ApiModelProperty(example = "1", value = "Código de actividad del tercero")
  public Integer getCodActTercero() {
    return codActTercero;
  }

  public void setCodActTercero(Integer codActTercero) {
    this.codActTercero = codActTercero;
  }

  public ResponsablePagoResponse tipDocum(String tipDocum) {
    this.tipDocum = tipDocum;
    return this;
  }

  @ApiModelProperty(example = "DNI", value = "Tipo de documento")
  public String getTipDocum() {
    return tipDocum;
  }

  public void setTipDocum(String tipDocum) {
    this.tipDocum = tipDocum;
  }

  public ResponsablePagoResponse codDocum(String codDocum) {
    this.codDocum = codDocum;
    return this;
  }

  @ApiModelProperty(example = "1413193300015", value = "Código de documento")
  public String getCodDocum() {
    return codDocum;
  }

  public void setCodDocum(String codDocum) {
    this.codDocum = codDocum;
  }

  public ResponsablePagoResponse nombreCompleto(String nombreCompleto) {
    this.nombreCompleto = nombreCompleto;
    return this;
  }

  @ApiModelProperty(example = "JUAN PÉREZ GARCÍA", value = "Nombre completo del responsable")
  public String getNombreCompleto() {
    return nombreCompleto;
  }

  public void setNombreCompleto(String nombreCompleto) {
    this.nombreCompleto = nombreCompleto;
  }

  public ResponsablePagoResponse direcCobro(String direcCobro) {
    this.direcCobro = direcCobro;
    return this;
  }

  @ApiModelProperty(example = "CALLE PRINCIPAL 123, COLONIA CENTRO", value = "Dirección de cobro")
  public String getDirecCobro() {
    return direcCobro;
  }

  public void setDirecCobro(String direcCobro) {
    this.direcCobro = direcCobro;
  }

  public ResponsablePagoResponse email(String email) {
    this.email = email;
    return this;
  }

  @ApiModelProperty(example = "<EMAIL>", value = "Correo electrónico")
  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ResponsablePagoResponse responsablePagoResponse = (ResponsablePagoResponse) o;
    return Objects.equals(this.codCia, responsablePagoResponse.codCia) &&
        Objects.equals(this.codActTercero, responsablePagoResponse.codActTercero) &&
        Objects.equals(this.tipDocum, responsablePagoResponse.tipDocum) &&
        Objects.equals(this.codDocum, responsablePagoResponse.codDocum) &&
        Objects.equals(this.nombreCompleto, responsablePagoResponse.nombreCompleto) &&
        Objects.equals(this.direcCobro, responsablePagoResponse.direcCobro) &&
        Objects.equals(this.email, responsablePagoResponse.email);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codCia, codActTercero, tipDocum, codDocum, nombreCompleto, direcCobro, email);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ResponsablePagoResponse {\n");
    
    sb.append("    codCia: ").append(toIndentedString(codCia)).append("\n");
    sb.append("    codActTercero: ").append(toIndentedString(codActTercero)).append("\n");
    sb.append("    tipDocum: ").append(toIndentedString(tipDocum)).append("\n");
    sb.append("    codDocum: ").append(toIndentedString(codDocum)).append("\n");
    sb.append("    nombreCompleto: ").append(toIndentedString(nombreCompleto)).append("\n");
    sb.append("    direcCobro: ").append(toIndentedString(direcCobro)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
