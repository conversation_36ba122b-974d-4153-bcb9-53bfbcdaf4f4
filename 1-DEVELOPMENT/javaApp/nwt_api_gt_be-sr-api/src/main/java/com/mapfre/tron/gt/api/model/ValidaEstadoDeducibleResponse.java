package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ValidaEstadoDeducibleResponse
 */
@Validated

public class ValidaEstadoDeducibleResponse   {
  @JsonProperty("resp")
  private String resp = null;

  public ValidaEstadoDeducibleResponse resp(String resp) {
    this.resp = resp;
    return this;
  }

  /**
   * Respuesta de la validación del estado del deducible
   * @return resp
  **/
  @ApiModelProperty(example = "info-Existe más de un deducible a pagar. Comuniniquese con el area de reclamos", value = "Respuesta de la validación del estado del deducible")


  public String getResp() {
    return resp;
  }

  public void setResp(String resp) {
    this.resp = resp;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ValidaEstadoDeducibleResponse validaEstadoDeducibleResponse = (ValidaEstadoDeducibleResponse) o;
    return Objects.equals(this.resp, validaEstadoDeducibleResponse.resp);
  }

  @Override
  public int hashCode() {
    return Objects.hash(resp);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ValidaEstadoDeducibleResponse {\n");
    
    sb.append("    resp: ").append(toIndentedString(resp)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
