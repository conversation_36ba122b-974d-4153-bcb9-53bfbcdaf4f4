/**
 * NOTE: This class is auto generated by the swagger code generator program (2.4.32).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.mapfre.tron.gt.api.sr;

import com.mapfre.tron.gt.api.model.ValidaEstadoDeducibleResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleFacResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleResponse;
import com.mapfre.tron.gt.api.model.InfoDeducibleResponse;
import com.mapfre.tron.gt.api.commons.exceptions.model.Error;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2025-01-02T15:30:00.000Z")

@Validated
@Api(value = "transacPagos", description = "the transacPagos API")
public interface TransacPagosApi {

    Logger log = LoggerFactory.getLogger(TransacPagosApi.class);

    default Optional<ObjectMapper> getObjectMapper() {
        return Optional.empty();
    }

    default Optional<HttpServletRequest> getRequest() {
        return Optional.empty();
    }

    default Optional<String> getAcceptHeader() {
        return getRequest().map(r -> r.getHeader("Accept"));
    }

    @ApiOperation(value = "Validar estado del deducible", nickname = "validaEstadoDeducible", notes = "Valida el estado del deducible para un siniestro específico", response = ValidaEstadoDeducibleResponse.class, tags={ "TransacPagos", })
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Operación exitosa", response = ValidaEstadoDeducibleResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/transacPagos/valida_estado_deducible",
        produces = { "application/json" },
        method = RequestMethod.GET)
    default ResponseEntity<ValidaEstadoDeducibleResponse> validaEstadoDeducible(@NotNull @ApiParam(value = "Código de compañía", required = true) @Valid @RequestParam(value = "codCia", required = true) Integer codCia,@NotNull @ApiParam(value = "Número de siniestro", required = true) @Valid @RequestParam(value = "numSini", required = true) String numSini) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"resp\" : \"info-Existe más de un deducible a pagar. Comuniniquese con el area de reclamos\"}", ValidaEstadoDeducibleResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TransacPagosApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @ApiOperation(value = "Obtener monto del deducible para facturación", nickname = "getMontoDeducibleFac", notes = "Obtiene el monto del deducible para facturación de un siniestro específico", response = MontoDeducibleFacResponse.class, tags={ "TransacPagos", })
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Operación exitosa", response = MontoDeducibleFacResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/transacPagos/get_mto_deducible_fac",
        produces = { "application/json" },
        method = RequestMethod.GET)
    default ResponseEntity<MontoDeducibleFacResponse> getMontoDeducibleFac(@NotNull @ApiParam(value = "Código de compañía", required = true) @Valid @RequestParam(value = "codCia", required = true) Integer codCia,@NotNull @ApiParam(value = "Número de siniestro", required = true) @Valid @RequestParam(value = "numSini", required = true) String numSini,@NotNull @ApiParam(value = "Marca de factura ('S' o 'N')", required = true) @Valid @RequestParam(value = "mcaFactura", required = true) String mcaFactura,@NotNull @ApiParam(value = "Número de cuota", required = true) @Valid @RequestParam(value = "numCuota", required = true) Integer numCuota) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"moneda\" : \"HNL\",  \"mtoDeducible\" : null,  \"mtoIva\" : null,  \"mtoCuotas\" : 0,  \"mtoTotal\" : null}", MontoDeducibleFacResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TransacPagosApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @ApiOperation(value = "Obtener monto del deducible", nickname = "getMontoDeducible", notes = "Obtiene el monto del deducible de un siniestro específico", response = MontoDeducibleResponse.class, tags={ "TransacPagos", })
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Operación exitosa", response = MontoDeducibleResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/transacPagos/get_mto_deducible",
        produces = { "application/json" },
        method = RequestMethod.GET)
    default ResponseEntity<MontoDeducibleResponse> getMontoDeducible(@NotNull @ApiParam(value = "Código de compañía", required = true) @Valid @RequestParam(value = "codCia", required = true) Integer codCia,@NotNull @ApiParam(value = "Número de siniestro", required = true) @Valid @RequestParam(value = "numSini", required = true) String numSini) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"monto\" : 1500.0}", MontoDeducibleResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TransacPagosApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @ApiOperation(value = "Obtener información del deducible", nickname = "getInfoDeducible", notes = "Obtiene la información de liquidación del deducible", response = InfoDeducibleResponse.class, tags={ "TransacPagos", })
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Operación exitosa", response = InfoDeducibleResponse.class),
        @ApiResponse(code = 400, message = "Bad request", response = Error.class),
        @ApiResponse(code = 401, message = "Not authorized", response = Error.class),
        @ApiResponse(code = 403, message = "Forbidden", response = Error.class),
        @ApiResponse(code = 404, message = "Not found", response = Error.class),
        @ApiResponse(code = 500, message = "Uncontrolled error", response = Error.class) })
    @RequestMapping(value = "/transacPagos/get_info_deducible",
        produces = { "application/json" },
        method = RequestMethod.GET)
    default ResponseEntity<InfoDeducibleResponse> getInfoDeducible(@NotNull @ApiParam(value = "Código de compañía", required = true) @Valid @RequestParam(value = "codCia", required = true) Integer codCia,@NotNull @ApiParam(value = "Número de liquidación", required = true) @Valid @RequestParam(value = "numLiq", required = true) String numLiq) {
        if(getObjectMapper().isPresent() && getAcceptHeader().isPresent()) {
            if (getAcceptHeader().get().contains("application/json")) {
                try {
                    return new ResponseEntity<>(getObjectMapper().get().readValue("{  \"numLiq\" : \"110123003434\",  \"numSini\" : \"110130023003755\",  \"numExp\" : 3,  \"numPoliza\" : \"0830023005036\",  \"codNivel3\" : \"1101\",  \"nomNivel3\" : \"CASA MATRIZ\",  \"codRamo\" : 300,  \"codSector\" : 3,  \"nomSector\" : \"AUTOS / MOTOR\",  \"codTercero\" : null,  \"nomTercero\" : \"FARIÑEZ, , ANGÉLICA\",  \"codActTercero\" : 1,  \"nomActTercero\" : \"ASEGURADOS/INSURERS\",  \"tipDocum\" : \"CED\",  \"codDocum\" : \"CED123456\",  \"obs\" : null,  \"fecLiq\" : \"29/05/2023\",  \"fecEstPago\" : \"29/05/2023\",  \"fecPago\" : \"27/05/2023\",  \"codMonLiq\" : 1,  \"codMonLiqIso\" : \"PAB\",  \"numDecimales\" : 2,  \"codMonPago\" : 1,  \"codMonPagoIso\" : \"PAB\",  \"impLiqNeto\" : 6000,  \"impIva\" : 0,  \"impLiq\" : 6000,  \"valCambio\" : 1,  \"tipDocto\" : \"IN\"}", InfoDeducibleResponse.class), HttpStatus.NOT_IMPLEMENTED);
                } catch (IOException e) {
                    log.error("Couldn't serialize response for content type application/json", e);
                    return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
                }
            }
        } else {
            log.warn("ObjectMapper or HttpServletRequest not configured in default TransacPagosApi interface so no example is generated");
        }
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

}
