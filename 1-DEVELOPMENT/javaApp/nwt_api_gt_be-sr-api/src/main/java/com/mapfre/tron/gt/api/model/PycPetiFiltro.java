package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycPetiFiltro
 */
@Validated

public class PycPetiFiltro   {
  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("nombrePeticion")
  private String nombrePeticion = null;

  @JsonProperty("descripcionPeticion")
  private String descripcionPeticion = null;

  @JsonProperty("fechaCreacion")
  private String fechaCreacion = null;

  @JsonProperty("idTipo")
  private Integer idTipo = null;

  @JsonProperty("idUsuarioSolicitante")
  private Integer idUsuarioSolicitante = null;

  @JsonProperty("usuario")
  private String usuario = null;

  @JsonProperty("fechaInicio")
  private String fechaInicio = null;

  @JsonProperty("fechaFin")
  private String fechaFin = null;

  @JsonProperty("totalHoras")
  private Double totalHoras = null;

  @JsonProperty("porcentajeBase")
  private Double porcentajeBase = null;

  @JsonProperty("porcentajeReal")
  private Double porcentajeReal = null;

  @JsonProperty("idEstado")
  private Integer idEstado = null;

  @JsonProperty("idPerfil")
  private Integer idPerfil = null;

  @JsonProperty("nombreDepartamento")
  private String nombreDepartamento = null;

  @JsonProperty("nombreArea")
  private String nombreArea = null;

  @JsonProperty("nombrePerfil")
  private String nombrePerfil = null;

  @JsonProperty("nombreEstado")
  private String nombreEstado = null;

  @JsonProperty("descripcionTipoPeticion")
  private String descripcionTipoPeticion = null;

  @JsonProperty("origen")
  private String origen = null;

  @JsonProperty("codClarity")
  private String codClarity = null;

  @JsonProperty("codcia")
  private String codcia = null;

  @JsonProperty("prioridad")
  private String prioridad = null;

  @JsonProperty("idPeticionSiguiente")
  private Integer idPeticionSiguiente = null;

  public PycPetiFiltro idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador de la petición
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", value = "Identificador de la petición")


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycPetiFiltro nombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
    return this;
  }

  /**
   * Nombre de la petición
   * @return nombrePeticion
  **/
  @ApiModelProperty(example = "Desarrollo de nueva funcionalidad", value = "Nombre de la petición")


  public String getNombrePeticion() {
    return nombrePeticion;
  }

  public void setNombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
  }

  public PycPetiFiltro descripcionPeticion(String descripcionPeticion) {
    this.descripcionPeticion = descripcionPeticion;
    return this;
  }

  /**
   * Descripción detallada de la petición
   * @return descripcionPeticion
  **/
  @ApiModelProperty(example = "Implementación de módulo de reportes", value = "Descripción detallada de la petición")


  public String getDescripcionPeticion() {
    return descripcionPeticion;
  }

  public void setDescripcionPeticion(String descripcionPeticion) {
    this.descripcionPeticion = descripcionPeticion;
  }

  public PycPetiFiltro fechaCreacion(String fechaCreacion) {
    this.fechaCreacion = fechaCreacion;
    return this;
  }

  /**
   * Fecha de creación de la petición
   * @return fechaCreacion
  **/
  @ApiModelProperty(example = "2024-01-15T10:30:00Z", value = "Fecha de creación de la petición")


  public String getFechaCreacion() {
    return fechaCreacion;
  }

  public void setFechaCreacion(String fechaCreacion) {
    this.fechaCreacion = fechaCreacion;
  }

  public PycPetiFiltro idTipo(Integer idTipo) {
    this.idTipo = idTipo;
    return this;
  }

  /**
   * Identificador del tipo de petición
   * @return idTipo
  **/
  @ApiModelProperty(example = "1", value = "Identificador del tipo de petición")


  public Integer getIdTipo() {
    return idTipo;
  }

  public void setIdTipo(Integer idTipo) {
    this.idTipo = idTipo;
  }

  public PycPetiFiltro idUsuarioSolicitante(Integer idUsuarioSolicitante) {
    this.idUsuarioSolicitante = idUsuarioSolicitante;
    return this;
  }

  /**
   * Identificador del usuario solicitante
   * @return idUsuarioSolicitante
  **/
  @ApiModelProperty(example = "1234", value = "Identificador del usuario solicitante")


  public Integer getIdUsuarioSolicitante() {
    return idUsuarioSolicitante;
  }

  public void setIdUsuarioSolicitante(Integer idUsuarioSolicitante) {
    this.idUsuarioSolicitante = idUsuarioSolicitante;
  }

  public PycPetiFiltro usuario(String usuario) {
    this.usuario = usuario;
    return this;
  }

  /**
   * Nombre completo del usuario solicitante
   * @return usuario
  **/
  @ApiModelProperty(example = "Juan Pérez", value = "Nombre completo del usuario solicitante")


  public String getUsuario() {
    return usuario;
  }

  public void setUsuario(String usuario) {
    this.usuario = usuario;
  }

  public PycPetiFiltro fechaInicio(String fechaInicio) {
    this.fechaInicio = fechaInicio;
    return this;
  }

  /**
   * Fecha de inicio de la petición
   * @return fechaInicio
  **/
  @ApiModelProperty(example = "2024-01-20T08:00:00Z", value = "Fecha de inicio de la petición")


  public String getFechaInicio() {
    return fechaInicio;
  }

  public void setFechaInicio(String fechaInicio) {
    this.fechaInicio = fechaInicio;
  }

  public PycPetiFiltro fechaFin(String fechaFin) {
    this.fechaFin = fechaFin;
    return this;
  }

  /**
   * Fecha de fin de la petición
   * @return fechaFin
  **/
  @ApiModelProperty(example = "2024-02-20T18:00:00Z", value = "Fecha de fin de la petición")


  public String getFechaFin() {
    return fechaFin;
  }

  public void setFechaFin(String fechaFin) {
    this.fechaFin = fechaFin;
  }

  public PycPetiFiltro totalHoras(Double totalHoras) {
    this.totalHoras = totalHoras;
    return this;
  }

  /**
   * Total de horas estimadas
   * @return totalHoras
  **/
  @ApiModelProperty(example = "120.5", value = "Total de horas estimadas")


  public Double getTotalHoras() {
    return totalHoras;
  }

  public void setTotalHoras(Double totalHoras) {
    this.totalHoras = totalHoras;
  }

  public PycPetiFiltro porcentajeBase(Double porcentajeBase) {
    this.porcentajeBase = porcentajeBase;
    return this;
  }

  /**
   * Porcentaje base de la petición
   * @return porcentajeBase
  **/
  @ApiModelProperty(example = "85.0", value = "Porcentaje base de la petición")


  public Double getPorcentajeBase() {
    return porcentajeBase;
  }

  public void setPorcentajeBase(Double porcentajeBase) {
    this.porcentajeBase = porcentajeBase;
  }

  public PycPetiFiltro porcentajeReal(Double porcentajeReal) {
    this.porcentajeReal = porcentajeReal;
    return this;
  }

  /**
   * Porcentaje real de avance
   * @return porcentajeReal
  **/
  @ApiModelProperty(example = "75.5", value = "Porcentaje real de avance")


  public Double getPorcentajeReal() {
    return porcentajeReal;
  }

  public void setPorcentajeReal(Double porcentajeReal) {
    this.porcentajeReal = porcentajeReal;
  }

  public PycPetiFiltro idEstado(Integer idEstado) {
    this.idEstado = idEstado;
    return this;
  }

  /**
   * Identificador del estado
   * @return idEstado
  **/
  @ApiModelProperty(example = "2", value = "Identificador del estado")


  public Integer getIdEstado() {
    return idEstado;
  }

  public void setIdEstado(Integer idEstado) {
    this.idEstado = idEstado;
  }

  public PycPetiFiltro idPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
    return this;
  }

  /**
   * Identificador del perfil
   * @return idPerfil
  **/
  @ApiModelProperty(example = "5", value = "Identificador del perfil")


  public Integer getIdPerfil() {
    return idPerfil;
  }

  public void setIdPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
  }

  public PycPetiFiltro nombreDepartamento(String nombreDepartamento) {
    this.nombreDepartamento = nombreDepartamento;
    return this;
  }

  /**
   * Nombre del departamento
   * @return nombreDepartamento
  **/
  @ApiModelProperty(example = "Desarrollo de Software", value = "Nombre del departamento")


  public String getNombreDepartamento() {
    return nombreDepartamento;
  }

  public void setNombreDepartamento(String nombreDepartamento) {
    this.nombreDepartamento = nombreDepartamento;
  }

  public PycPetiFiltro nombreArea(String nombreArea) {
    this.nombreArea = nombreArea;
    return this;
  }

  /**
   * Nombre del área
   * @return nombreArea
  **/
  @ApiModelProperty(example = "Tecnología", value = "Nombre del área")


  public String getNombreArea() {
    return nombreArea;
  }

  public void setNombreArea(String nombreArea) {
    this.nombreArea = nombreArea;
  }

  public PycPetiFiltro nombrePerfil(String nombrePerfil) {
    this.nombrePerfil = nombrePerfil;
    return this;
  }

  /**
   * Nombre del perfil
   * @return nombrePerfil
  **/
  @ApiModelProperty(example = "Analista Senior", value = "Nombre del perfil")


  public String getNombrePerfil() {
    return nombrePerfil;
  }

  public void setNombrePerfil(String nombrePerfil) {
    this.nombrePerfil = nombrePerfil;
  }

  public PycPetiFiltro nombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
    return this;
  }

  /**
   * Nombre del estado
   * @return nombreEstado
  **/
  @ApiModelProperty(example = "En Progreso", value = "Nombre del estado")


  public String getNombreEstado() {
    return nombreEstado;
  }

  public void setNombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
  }

  public PycPetiFiltro descripcionTipoPeticion(String descripcionTipoPeticion) {
    this.descripcionTipoPeticion = descripcionTipoPeticion;
    return this;
  }

  /**
   * Descripción del tipo de petición
   * @return descripcionTipoPeticion
  **/
  @ApiModelProperty(example = "Desarrollo", value = "Descripción del tipo de petición")


  public String getDescripcionTipoPeticion() {
    return descripcionTipoPeticion;
  }

  public void setDescripcionTipoPeticion(String descripcionTipoPeticion) {
    this.descripcionTipoPeticion = descripcionTipoPeticion;
  }

  public PycPetiFiltro origen(String origen) {
    this.origen = origen;
    return this;
  }

  /**
   * Origen de la petición
   * @return origen
  **/
  @ApiModelProperty(example = "Cliente Interno", value = "Origen de la petición")


  public String getOrigen() {
    return origen;
  }

  public void setOrigen(String origen) {
    this.origen = origen;
  }

  public PycPetiFiltro codClarity(String codClarity) {
    this.codClarity = codClarity;
    return this;
  }

  /**
   * Código Clarity
   * @return codClarity
  **/
  @ApiModelProperty(example = "MU-2024-001234", value = "Código Clarity")


  public String getCodClarity() {
    return codClarity;
  }

  public void setCodClarity(String codClarity) {
    this.codClarity = codClarity;
  }

  public PycPetiFiltro codcia(String codcia) {
    this.codcia = codcia;
    return this;
  }

  /**
   * Código de compañía
   * @return codcia
  **/
  @ApiModelProperty(example = "2", value = "Código de compañía")


  public String getCodcia() {
    return codcia;
  }

  public void setCodcia(String codcia) {
    this.codcia = codcia;
  }

  public PycPetiFiltro prioridad(String prioridad) {
    this.prioridad = prioridad;
    return this;
  }

  /**
   * Prioridad de la petición
   * @return prioridad
  **/
  @ApiModelProperty(example = "1", value = "Prioridad de la petición")


  public String getPrioridad() {
    return prioridad;
  }

  public void setPrioridad(String prioridad) {
    this.prioridad = prioridad;
  }

  public PycPetiFiltro idPeticionSiguiente(Integer idPeticionSiguiente) {
    this.idPeticionSiguiente = idPeticionSiguiente;
    return this;
  }

  /**
   * Identificador de la petición siguiente
   * @return idPeticionSiguiente
  **/
  @ApiModelProperty(example = "1002", value = "Identificador de la petición siguiente")


  public Integer getIdPeticionSiguiente() {
    return idPeticionSiguiente;
  }

  public void setIdPeticionSiguiente(Integer idPeticionSiguiente) {
    this.idPeticionSiguiente = idPeticionSiguiente;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycPetiFiltro pycPetiFiltro = (PycPetiFiltro) o;
    return Objects.equals(this.idPeticion, pycPetiFiltro.idPeticion) &&
        Objects.equals(this.nombrePeticion, pycPetiFiltro.nombrePeticion) &&
        Objects.equals(this.descripcionPeticion, pycPetiFiltro.descripcionPeticion) &&
        Objects.equals(this.fechaCreacion, pycPetiFiltro.fechaCreacion) &&
        Objects.equals(this.idTipo, pycPetiFiltro.idTipo) &&
        Objects.equals(this.idUsuarioSolicitante, pycPetiFiltro.idUsuarioSolicitante) &&
        Objects.equals(this.usuario, pycPetiFiltro.usuario) &&
        Objects.equals(this.fechaInicio, pycPetiFiltro.fechaInicio) &&
        Objects.equals(this.fechaFin, pycPetiFiltro.fechaFin) &&
        Objects.equals(this.totalHoras, pycPetiFiltro.totalHoras) &&
        Objects.equals(this.porcentajeBase, pycPetiFiltro.porcentajeBase) &&
        Objects.equals(this.porcentajeReal, pycPetiFiltro.porcentajeReal) &&
        Objects.equals(this.idEstado, pycPetiFiltro.idEstado) &&
        Objects.equals(this.idPerfil, pycPetiFiltro.idPerfil) &&
        Objects.equals(this.nombreDepartamento, pycPetiFiltro.nombreDepartamento) &&
        Objects.equals(this.nombreArea, pycPetiFiltro.nombreArea) &&
        Objects.equals(this.nombrePerfil, pycPetiFiltro.nombrePerfil) &&
        Objects.equals(this.nombreEstado, pycPetiFiltro.nombreEstado) &&
        Objects.equals(this.descripcionTipoPeticion, pycPetiFiltro.descripcionTipoPeticion) &&
        Objects.equals(this.origen, pycPetiFiltro.origen) &&
        Objects.equals(this.codClarity, pycPetiFiltro.codClarity) &&
        Objects.equals(this.codcia, pycPetiFiltro.codcia) &&
        Objects.equals(this.prioridad, pycPetiFiltro.prioridad) &&
        Objects.equals(this.idPeticionSiguiente, pycPetiFiltro.idPeticionSiguiente);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, nombrePeticion, descripcionPeticion, fechaCreacion, idTipo, idUsuarioSolicitante, usuario, fechaInicio, fechaFin, totalHoras, porcentajeBase, porcentajeReal, idEstado, idPerfil, nombreDepartamento, nombreArea, nombrePerfil, nombreEstado, descripcionTipoPeticion, origen, codClarity, codcia, prioridad, idPeticionSiguiente);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycPetiFiltro {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    nombrePeticion: ").append(toIndentedString(nombrePeticion)).append("\n");
    sb.append("    descripcionPeticion: ").append(toIndentedString(descripcionPeticion)).append("\n");
    sb.append("    fechaCreacion: ").append(toIndentedString(fechaCreacion)).append("\n");
    sb.append("    idTipo: ").append(toIndentedString(idTipo)).append("\n");
    sb.append("    idUsuarioSolicitante: ").append(toIndentedString(idUsuarioSolicitante)).append("\n");
    sb.append("    usuario: ").append(toIndentedString(usuario)).append("\n");
    sb.append("    fechaInicio: ").append(toIndentedString(fechaInicio)).append("\n");
    sb.append("    fechaFin: ").append(toIndentedString(fechaFin)).append("\n");
    sb.append("    totalHoras: ").append(toIndentedString(totalHoras)).append("\n");
    sb.append("    porcentajeBase: ").append(toIndentedString(porcentajeBase)).append("\n");
    sb.append("    porcentajeReal: ").append(toIndentedString(porcentajeReal)).append("\n");
    sb.append("    idEstado: ").append(toIndentedString(idEstado)).append("\n");
    sb.append("    idPerfil: ").append(toIndentedString(idPerfil)).append("\n");
    sb.append("    nombreDepartamento: ").append(toIndentedString(nombreDepartamento)).append("\n");
    sb.append("    nombreArea: ").append(toIndentedString(nombreArea)).append("\n");
    sb.append("    nombrePerfil: ").append(toIndentedString(nombrePerfil)).append("\n");
    sb.append("    nombreEstado: ").append(toIndentedString(nombreEstado)).append("\n");
    sb.append("    descripcionTipoPeticion: ").append(toIndentedString(descripcionTipoPeticion)).append("\n");
    sb.append("    origen: ").append(toIndentedString(origen)).append("\n");
    sb.append("    codClarity: ").append(toIndentedString(codClarity)).append("\n");
    sb.append("    codcia: ").append(toIndentedString(codcia)).append("\n");
    sb.append("    prioridad: ").append(toIndentedString(prioridad)).append("\n");
    sb.append("    idPeticionSiguiente: ").append(toIndentedString(idPeticionSiguiente)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

