package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * InfoReciboResponse
 */
@Validated

public class InfoReciboResponse   {
  @JsonProperty("codCia")
  private Integer codCia = null;

  @JsonProperty("numPoliza")
  private String numPoliza = null;

  @JsonProperty("numRecibo")
  private Integer numRecibo = null;

  @JsonProperty("codMon")
  private Integer codMon = null;

  @JsonProperty("impRecibo")
  private Double impRecibo = null;

  @JsonProperty("tipSituacion")
  private String tipSituacion = null;

  @JsonProperty("fecVctoRecibo")
  private String fecVctoRecibo = null;

  @JsonProperty("fecSituacion")
  private String fecSituacion = null;

  public InfoReciboResponse codCia(Integer codCia) {
    this.codCia = codCia;
    return this;
  }

  /**
   * Código de compañía
   * @return codCia
  **/
  @ApiModelProperty(example = "2", value = "Código de compañía")


  public Integer getCodCia() {
    return codCia;
  }

  public void setCodCia(Integer codCia) {
    this.codCia = codCia;
  }

  public InfoReciboResponse numPoliza(String numPoliza) {
    this.numPoliza = numPoliza;
    return this;
  }

  /**
   * Número de póliza
   * @return numPoliza
  **/
  @ApiModelProperty(example = "0270025001107", value = "Número de póliza")


  public String getNumPoliza() {
    return numPoliza;
  }

  public void setNumPoliza(String numPoliza) {
    this.numPoliza = numPoliza;
  }

  public InfoReciboResponse numRecibo(Integer numRecibo) {
    this.numRecibo = numRecibo;
    return this;
  }

  /**
   * Número de recibo
   * @return numRecibo
  **/
  @ApiModelProperty(example = "112955", value = "Número de recibo")


  public Integer getNumRecibo() {
    return numRecibo;
  }

  public void setNumRecibo(Integer numRecibo) {
    this.numRecibo = numRecibo;
  }

  public InfoReciboResponse codMon(Integer codMon) {
    this.codMon = codMon;
    return this;
  }

  /**
   * Código de moneda
   * @return codMon
  **/
  @ApiModelProperty(example = "4", value = "Código de moneda")


  public Integer getCodMon() {
    return codMon;
  }

  public void setCodMon(Integer codMon) {
    this.codMon = codMon;
  }

  public InfoReciboResponse impRecibo(Double impRecibo) {
    this.impRecibo = impRecibo;
    return this;
  }

  /**
   * Importe del recibo
   * @return impRecibo
  **/
  @ApiModelProperty(example = "5125.0", value = "Importe del recibo")


  public Double getImpRecibo() {
    return impRecibo;
  }

  public void setImpRecibo(Double impRecibo) {
    this.impRecibo = impRecibo;
  }

  public InfoReciboResponse tipSituacion(String tipSituacion) {
    this.tipSituacion = tipSituacion;
    return this;
  }

  /**
   * Tipo de situación
   * @return tipSituacion
  **/
  @ApiModelProperty(example = "CT", value = "Tipo de situación")


  public String getTipSituacion() {
    return tipSituacion;
  }

  public void setTipSituacion(String tipSituacion) {
    this.tipSituacion = tipSituacion;
  }

  public InfoReciboResponse fecVctoRecibo(String fecVctoRecibo) {
    this.fecVctoRecibo = fecVctoRecibo;
    return this;
  }

  /**
   * Fecha de vencimiento del recibo
   * @return fecVctoRecibo
  **/
  @ApiModelProperty(example = "01-JAN-26", value = "Fecha de vencimiento del recibo")


  public String getFecVctoRecibo() {
    return fecVctoRecibo;
  }

  public void setFecVctoRecibo(String fecVctoRecibo) {
    this.fecVctoRecibo = fecVctoRecibo;
  }

  public InfoReciboResponse fecSituacion(String fecSituacion) {
    this.fecSituacion = fecSituacion;
    return this;
  }

  /**
   * Fecha de situación
   * @return fecSituacion
  **/
  @ApiModelProperty(example = "19-MAY-25", value = "Fecha de situación")


  public String getFecSituacion() {
    return fecSituacion;
  }

  public void setFecSituacion(String fecSituacion) {
    this.fecSituacion = fecSituacion;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InfoReciboResponse infoReciboResponse = (InfoReciboResponse) o;
    return Objects.equals(this.codCia, infoReciboResponse.codCia) &&
        Objects.equals(this.numPoliza, infoReciboResponse.numPoliza) &&
        Objects.equals(this.numRecibo, infoReciboResponse.numRecibo) &&
        Objects.equals(this.codMon, infoReciboResponse.codMon) &&
        Objects.equals(this.impRecibo, infoReciboResponse.impRecibo) &&
        Objects.equals(this.tipSituacion, infoReciboResponse.tipSituacion) &&
        Objects.equals(this.fecVctoRecibo, infoReciboResponse.fecVctoRecibo) &&
        Objects.equals(this.fecSituacion, infoReciboResponse.fecSituacion);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codCia, numPoliza, numRecibo, codMon, impRecibo, tipSituacion, fecVctoRecibo, fecSituacion);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InfoReciboResponse {\n");
    
    sb.append("    codCia: ").append(toIndentedString(codCia)).append("\n");
    sb.append("    numPoliza: ").append(toIndentedString(numPoliza)).append("\n");
    sb.append("    numRecibo: ").append(toIndentedString(numRecibo)).append("\n");
    sb.append("    codMon: ").append(toIndentedString(codMon)).append("\n");
    sb.append("    impRecibo: ").append(toIndentedString(impRecibo)).append("\n");
    sb.append("    tipSituacion: ").append(toIndentedString(tipSituacion)).append("\n");
    sb.append("    fecVctoRecibo: ").append(toIndentedString(fecVctoRecibo)).append("\n");
    sb.append("    fecSituacion: ").append(toIndentedString(fecSituacion)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

