package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * MontoDeducibleResponse
 */
@Validated

public class MontoDeducibleResponse   {
  @JsonProperty("monto")
  private Double monto = null;

  public MontoDeducibleResponse monto(Double monto) {
    this.monto = monto;
    return this;
  }

  /**
   * Monto del deducible obtenido
   * @return monto
  **/
  @ApiModelProperty(example = "1500.0", value = "Monto del deducible obtenido")


  public Double getMonto() {
    return monto;
  }

  public void setMonto(Double monto) {
    this.monto = monto;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MontoDeducibleResponse montoDeducibleResponse = (MontoDeducibleResponse) o;
    return Objects.equals(this.monto, montoDeducibleResponse.monto);
  }

  @Override
  public int hashCode() {
    return Objects.hash(monto);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MontoDeducibleResponse {\n");
    
    sb.append("    monto: ").append(toIndentedString(monto)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

