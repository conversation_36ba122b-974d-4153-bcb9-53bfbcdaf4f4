package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycInsertPeticion
 */
@Validated

public class PycInsertPeticion   {
  @JsonProperty("nombrePeticion")
  private String nombrePeticion = null;

  @JsonProperty("descripcionPeticion")
  private String descripcionPeticion = null;

  @JsonProperty("idUsuarioSolicitante")
  private Integer idUsuarioSolicitante = null;

  @JsonProperty("idPerfil")
  private Integer idPerfil = null;

  @JsonProperty("idTipo")
  private Integer idTipo = null;

  @JsonProperty("idProceso")
  private Integer idProceso = null;

  @JsonProperty("codcia")
  private String codcia = null;

  @JsonProperty("nombreCliente")
  private String nombreCliente = null;

  @JsonProperty("telefonoCliente")
  private String telefonoCliente = null;

  @JsonProperty("correoCliente")
  private String correoCliente = null;

  @JsonProperty("origen")
  private String origen = null;

  @JsonProperty("codClarity")
  private String codClarity = null;

  @JsonProperty("prioridad")
  private String prioridad = null;

  @JsonProperty("tipoCliente")
  private String tipoCliente = null;

  @JsonProperty("codCliente")
  private String codCliente = null;

  @JsonProperty("noPoliza")
  private String noPoliza = null;

  @JsonProperty("tipServicio")
  private String tipServicio = null;

  @JsonProperty("causa")
  private String causa = null;

  @JsonProperty("gravedad")
  private String gravedad = null;

  @JsonProperty("idReferencia")
  private String idReferencia = null;

  @JsonProperty("codinter")
  private String codinter = null;

  @JsonProperty("xmlText")
  private String xmlText = null;

  @JsonProperty("usuarioReg")
  private String usuarioReg = null;

  @JsonProperty("asigAuto")
  private Boolean asigAuto = null;

  public PycInsertPeticion nombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
    return this;
  }

  /**
   * Nombre de la petición
   * @return nombrePeticion
  **/
  @ApiModelProperty(example = "Solicitud de cambio de datos", required = true, value = "Nombre de la petición")
  @NotNull


  public String getNombrePeticion() {
    return nombrePeticion;
  }

  public void setNombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
  }

  public PycInsertPeticion descripcionPeticion(String descripcionPeticion) {
    this.descripcionPeticion = descripcionPeticion;
    return this;
  }

  /**
   * Descripción detallada de la petición
   * @return descripcionPeticion
  **/
  @ApiModelProperty(example = "Solicitud para cambiar datos personales en la póliza", required = true, value = "Descripción detallada de la petición")
  @NotNull


  public String getDescripcionPeticion() {
    return descripcionPeticion;
  }

  public void setDescripcionPeticion(String descripcionPeticion) {
    this.descripcionPeticion = descripcionPeticion;
  }

  public PycInsertPeticion idUsuarioSolicitante(Integer idUsuarioSolicitante) {
    this.idUsuarioSolicitante = idUsuarioSolicitante;
    return this;
  }

  /**
   * ID del usuario solicitante
   * @return idUsuarioSolicitante
  **/
  @ApiModelProperty(example = "1234", required = true, value = "ID del usuario solicitante")
  @NotNull


  public Integer getIdUsuarioSolicitante() {
    return idUsuarioSolicitante;
  }

  public void setIdUsuarioSolicitante(Integer idUsuarioSolicitante) {
    this.idUsuarioSolicitante = idUsuarioSolicitante;
  }

  public PycInsertPeticion idPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
    return this;
  }

  /**
   * ID del perfil
   * @return idPerfil
  **/
  @ApiModelProperty(example = "1", required = true, value = "ID del perfil")
  @NotNull


  public Integer getIdPerfil() {
    return idPerfil;
  }

  public void setIdPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
  }

  public PycInsertPeticion idTipo(Integer idTipo) {
    this.idTipo = idTipo;
    return this;
  }

  /**
   * ID del tipo de petición
   * @return idTipo
  **/
  @ApiModelProperty(example = "2", required = true, value = "ID del tipo de petición")
  @NotNull


  public Integer getIdTipo() {
    return idTipo;
  }

  public void setIdTipo(Integer idTipo) {
    this.idTipo = idTipo;
  }

  public PycInsertPeticion idProceso(Integer idProceso) {
    this.idProceso = idProceso;
    return this;
  }

  /**
   * ID del proceso
   * @return idProceso
  **/
  @ApiModelProperty(example = "3", required = true, value = "ID del proceso")
  @NotNull


  public Integer getIdProceso() {
    return idProceso;
  }

  public void setIdProceso(Integer idProceso) {
    this.idProceso = idProceso;
  }

  public PycInsertPeticion codcia(String codcia) {
    this.codcia = codcia;
    return this;
  }

  /**
   * Código de compañía
   * @return codcia
  **/
  @ApiModelProperty(example = "2", required = true, value = "Código de compañía")
  @NotNull


  public String getCodcia() {
    return codcia;
  }

  public void setCodcia(String codcia) {
    this.codcia = codcia;
  }

  public PycInsertPeticion nombreCliente(String nombreCliente) {
    this.nombreCliente = nombreCliente;
    return this;
  }

  /**
   * Nombre del cliente
   * @return nombreCliente
  **/
  @ApiModelProperty(example = "Juan Carlos Pérez García", value = "Nombre del cliente")


  public String getNombreCliente() {
    return nombreCliente;
  }

  public void setNombreCliente(String nombreCliente) {
    this.nombreCliente = nombreCliente;
  }

  public PycInsertPeticion telefonoCliente(String telefonoCliente) {
    this.telefonoCliente = telefonoCliente;
    return this;
  }

  /**
   * Teléfono del cliente
   * @return telefonoCliente
  **/
  @ApiModelProperty(example = "22345678", value = "Teléfono del cliente")


  public String getTelefonoCliente() {
    return telefonoCliente;
  }

  public void setTelefonoCliente(String telefonoCliente) {
    this.telefonoCliente = telefonoCliente;
  }

  public PycInsertPeticion correoCliente(String correoCliente) {
    this.correoCliente = correoCliente;
    return this;
  }

  /**
   * Correo electrónico del cliente
   * @return correoCliente
  **/
  @ApiModelProperty(example = "<EMAIL>", value = "Correo electrónico del cliente")


  public String getCorreoCliente() {
    return correoCliente;
  }

  public void setCorreoCliente(String correoCliente) {
    this.correoCliente = correoCliente;
  }

  public PycInsertPeticion origen(String origen) {
    this.origen = origen;
    return this;
  }

  /**
   * Origen de la petición
   * @return origen
  **/
  @ApiModelProperty(example = "CO257", value = "Origen de la petición")


  public String getOrigen() {
    return origen;
  }

  public void setOrigen(String origen) {
    this.origen = origen;
  }

  public PycInsertPeticion codClarity(String codClarity) {
    this.codClarity = codClarity;
    return this;
  }

  /**
   * Código Clarity
   * @return codClarity
  **/
  @ApiModelProperty(example = "MU-2019-038493", value = "Código Clarity")


  public String getCodClarity() {
    return codClarity;
  }

  public void setCodClarity(String codClarity) {
    this.codClarity = codClarity;
  }

  public PycInsertPeticion prioridad(String prioridad) {
    this.prioridad = prioridad;
    return this;
  }

  /**
   * Prioridad de la petición
   * @return prioridad
  **/
  @ApiModelProperty(example = "1", value = "Prioridad de la petición")


  public String getPrioridad() {
    return prioridad;
  }

  public void setPrioridad(String prioridad) {
    this.prioridad = prioridad;
  }

  public PycInsertPeticion tipoCliente(String tipoCliente) {
    this.tipoCliente = tipoCliente;
    return this;
  }

  /**
   * Tipo de cliente
   * @return tipoCliente
  **/
  @ApiModelProperty(example = "2", value = "Tipo de cliente")


  public String getTipoCliente() {
    return tipoCliente;
  }

  public void setTipoCliente(String tipoCliente) {
    this.tipoCliente = tipoCliente;
  }

  public PycInsertPeticion codCliente(String codCliente) {
    this.codCliente = codCliente;
    return this;
  }

  /**
   * Código del cliente
   * @return codCliente
  **/
  @ApiModelProperty(example = "12345678", value = "Código del cliente")


  public String getCodCliente() {
    return codCliente;
  }

  public void setCodCliente(String codCliente) {
    this.codCliente = codCliente;
  }

  public PycInsertPeticion noPoliza(String noPoliza) {
    this.noPoliza = noPoliza;
    return this;
  }

  /**
   * Número de póliza
   * @return noPoliza
  **/
  @ApiModelProperty(example = "0230025018359", value = "Número de póliza")


  public String getNoPoliza() {
    return noPoliza;
  }

  public void setNoPoliza(String noPoliza) {
    this.noPoliza = noPoliza;
  }

  public PycInsertPeticion tipServicio(String tipServicio) {
    this.tipServicio = tipServicio;
    return this;
  }

  /**
   * Tipo de servicio
   * @return tipServicio
  **/
  @ApiModelProperty(example = "1", value = "Tipo de servicio")


  public String getTipServicio() {
    return tipServicio;
  }

  public void setTipServicio(String tipServicio) {
    this.tipServicio = tipServicio;
  }

  public PycInsertPeticion causa(String causa) {
    this.causa = causa;
    return this;
  }

  /**
   * Causa de la petición
   * @return causa
  **/
  @ApiModelProperty(example = "1", value = "Causa de la petición")


  public String getCausa() {
    return causa;
  }

  public void setCausa(String causa) {
    this.causa = causa;
  }

  public PycInsertPeticion gravedad(String gravedad) {
    this.gravedad = gravedad;
    return this;
  }

  /**
   * Gravedad de la petición
   * @return gravedad
  **/
  @ApiModelProperty(example = "1", value = "Gravedad de la petición")


  public String getGravedad() {
    return gravedad;
  }

  public void setGravedad(String gravedad) {
    this.gravedad = gravedad;
  }

  public PycInsertPeticion idReferencia(String idReferencia) {
    this.idReferencia = idReferencia;
    return this;
  }

  /**
   * ID de referencia
   * @return idReferencia
  **/
  @ApiModelProperty(example = "1", value = "ID de referencia")


  public String getIdReferencia() {
    return idReferencia;
  }

  public void setIdReferencia(String idReferencia) {
    this.idReferencia = idReferencia;
  }

  public PycInsertPeticion codinter(String codinter) {
    this.codinter = codinter;
    return this;
  }

  /**
   * Código de intermediario
   * @return codinter
  **/
  @ApiModelProperty(example = "900000", value = "Código de intermediario")


  public String getCodinter() {
    return codinter;
  }

  public void setCodinter(String codinter) {
    this.codinter = codinter;
  }

  public PycInsertPeticion xmlText(String xmlText) {
    this.xmlText = xmlText;
    return this;
  }

  /**
   * Datos adicionales en formato XML. Soporta XML directo o envuelto en CDATA para preservar formato. Ejemplos: '<INFO_PETICION></INFO_PETICION>' o '<![CDATA[<INFO_PETICION>          </INFO_PETICION>]]>'
   * @return xmlText
  **/
  @ApiModelProperty(example = "<INFO_PETICION></INFO_PETICION>", value = "Datos adicionales en formato XML. Soporta XML directo o envuelto en CDATA para preservar formato. Ejemplos: '<INFO_PETICION></INFO_PETICION>' o '<![CDATA[<INFO_PETICION>          </INFO_PETICION>]]>'")


  public String getXmlText() {
    return xmlText;
  }

  public void setXmlText(String xmlText) {
    this.xmlText = xmlText;
  }

  public PycInsertPeticion usuarioReg(String usuarioReg) {
    this.usuarioReg = usuarioReg;
    return this;
  }

  /**
   * Usuario que registra la petición
   * @return usuarioReg
  **/
  @ApiModelProperty(example = "ADMIN", value = "Usuario que registra la petición")


  public String getUsuarioReg() {
    return usuarioReg;
  }

  public void setUsuarioReg(String usuarioReg) {
    this.usuarioReg = usuarioReg;
  }

  public PycInsertPeticion asigAuto(Boolean asigAuto) {
    this.asigAuto = asigAuto;
    return this;
  }

  /**
   * Indicador de asignación automática
   * @return asigAuto
  **/
  @ApiModelProperty(example = "true", value = "Indicador de asignación automática")


  public Boolean isAsigAuto() {
    return asigAuto;
  }

  public void setAsigAuto(Boolean asigAuto) {
    this.asigAuto = asigAuto;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycInsertPeticion pycInsertPeticion = (PycInsertPeticion) o;
    return Objects.equals(this.nombrePeticion, pycInsertPeticion.nombrePeticion) &&
        Objects.equals(this.descripcionPeticion, pycInsertPeticion.descripcionPeticion) &&
        Objects.equals(this.idUsuarioSolicitante, pycInsertPeticion.idUsuarioSolicitante) &&
        Objects.equals(this.idPerfil, pycInsertPeticion.idPerfil) &&
        Objects.equals(this.idTipo, pycInsertPeticion.idTipo) &&
        Objects.equals(this.idProceso, pycInsertPeticion.idProceso) &&
        Objects.equals(this.codcia, pycInsertPeticion.codcia) &&
        Objects.equals(this.nombreCliente, pycInsertPeticion.nombreCliente) &&
        Objects.equals(this.telefonoCliente, pycInsertPeticion.telefonoCliente) &&
        Objects.equals(this.correoCliente, pycInsertPeticion.correoCliente) &&
        Objects.equals(this.origen, pycInsertPeticion.origen) &&
        Objects.equals(this.codClarity, pycInsertPeticion.codClarity) &&
        Objects.equals(this.prioridad, pycInsertPeticion.prioridad) &&
        Objects.equals(this.tipoCliente, pycInsertPeticion.tipoCliente) &&
        Objects.equals(this.codCliente, pycInsertPeticion.codCliente) &&
        Objects.equals(this.noPoliza, pycInsertPeticion.noPoliza) &&
        Objects.equals(this.tipServicio, pycInsertPeticion.tipServicio) &&
        Objects.equals(this.causa, pycInsertPeticion.causa) &&
        Objects.equals(this.gravedad, pycInsertPeticion.gravedad) &&
        Objects.equals(this.idReferencia, pycInsertPeticion.idReferencia) &&
        Objects.equals(this.codinter, pycInsertPeticion.codinter) &&
        Objects.equals(this.xmlText, pycInsertPeticion.xmlText) &&
        Objects.equals(this.usuarioReg, pycInsertPeticion.usuarioReg) &&
        Objects.equals(this.asigAuto, pycInsertPeticion.asigAuto);
  }

  @Override
  public int hashCode() {
    return Objects.hash(nombrePeticion, descripcionPeticion, idUsuarioSolicitante, idPerfil, idTipo, idProceso, codcia, nombreCliente, telefonoCliente, correoCliente, origen, codClarity, prioridad, tipoCliente, codCliente, noPoliza, tipServicio, causa, gravedad, idReferencia, codinter, xmlText, usuarioReg, asigAuto);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycInsertPeticion {\n");
    
    sb.append("    nombrePeticion: ").append(toIndentedString(nombrePeticion)).append("\n");
    sb.append("    descripcionPeticion: ").append(toIndentedString(descripcionPeticion)).append("\n");
    sb.append("    idUsuarioSolicitante: ").append(toIndentedString(idUsuarioSolicitante)).append("\n");
    sb.append("    idPerfil: ").append(toIndentedString(idPerfil)).append("\n");
    sb.append("    idTipo: ").append(toIndentedString(idTipo)).append("\n");
    sb.append("    idProceso: ").append(toIndentedString(idProceso)).append("\n");
    sb.append("    codcia: ").append(toIndentedString(codcia)).append("\n");
    sb.append("    nombreCliente: ").append(toIndentedString(nombreCliente)).append("\n");
    sb.append("    telefonoCliente: ").append(toIndentedString(telefonoCliente)).append("\n");
    sb.append("    correoCliente: ").append(toIndentedString(correoCliente)).append("\n");
    sb.append("    origen: ").append(toIndentedString(origen)).append("\n");
    sb.append("    codClarity: ").append(toIndentedString(codClarity)).append("\n");
    sb.append("    prioridad: ").append(toIndentedString(prioridad)).append("\n");
    sb.append("    tipoCliente: ").append(toIndentedString(tipoCliente)).append("\n");
    sb.append("    codCliente: ").append(toIndentedString(codCliente)).append("\n");
    sb.append("    noPoliza: ").append(toIndentedString(noPoliza)).append("\n");
    sb.append("    tipServicio: ").append(toIndentedString(tipServicio)).append("\n");
    sb.append("    causa: ").append(toIndentedString(causa)).append("\n");
    sb.append("    gravedad: ").append(toIndentedString(gravedad)).append("\n");
    sb.append("    idReferencia: ").append(toIndentedString(idReferencia)).append("\n");
    sb.append("    codinter: ").append(toIndentedString(codinter)).append("\n");
    sb.append("    xmlText: ").append(toIndentedString(xmlText)).append("\n");
    sb.append("    usuarioReg: ").append(toIndentedString(usuarioReg)).append("\n");
    sb.append("    asigAuto: ").append(toIndentedString(asigAuto)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

