package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.mapfre.tron.gt.api.model.TipoDocumentoPago;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * TipoDocumentoPagoResponse
 */
@Validated

public class TipoDocumentoPagoResponse   {
  @JsonProperty("tiposDocumento")
  @Valid
  private List<TipoDocumentoPago> tiposDocumento = null;

  @JsonProperty("codigo")
  private String codigo = null;

  @JsonProperty("mensaje")
  private String mensaje = null;

  public TipoDocumentoPagoResponse tiposDocumento(List<TipoDocumentoPago> tiposDocumento) {
    this.tiposDocumento = tiposDocumento;
    return this;
  }

  public TipoDocumentoPagoResponse addTiposDocumentoItem(TipoDocumentoPago tiposDocumentoItem) {
    if (this.tiposDocumento == null) {
      this.tiposDocumento = new ArrayList<>();
    }
    this.tiposDocumento.add(tiposDocumentoItem);
    return this;
  }

  /**
   * Lista de tipos de documentos de pago
   * @return tiposDocumento
  **/
  @ApiModelProperty(value = "Lista de tipos de documentos de pago")

  @Valid

  public List<TipoDocumentoPago> getTiposDocumento() {
    return tiposDocumento;
  }

  public void setTiposDocumento(List<TipoDocumentoPago> tiposDocumento) {
    this.tiposDocumento = tiposDocumento;
  }

  public TipoDocumentoPagoResponse codigo(String codigo) {
    this.codigo = codigo;
    return this;
  }

  /**
   * Código de resultado de la operación
   * @return codigo
  **/
  @ApiModelProperty(example = "200", value = "Código de resultado de la operación")


  public String getCodigo() {
    return codigo;
  }

  public void setCodigo(String codigo) {
    this.codigo = codigo;
  }

  public TipoDocumentoPagoResponse mensaje(String mensaje) {
    this.mensaje = mensaje;
    return this;
  }

  /**
   * Mensaje descriptivo del resultado de la operación
   * @return mensaje
  **/
  @ApiModelProperty(example = "Operación exitosa", value = "Mensaje descriptivo del resultado de la operación")


  public String getMensaje() {
    return mensaje;
  }

  public void setMensaje(String mensaje) {
    this.mensaje = mensaje;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TipoDocumentoPagoResponse tipoDocumentoPagoResponse = (TipoDocumentoPagoResponse) o;
    return Objects.equals(this.tiposDocumento, tipoDocumentoPagoResponse.tiposDocumento) &&
        Objects.equals(this.codigo, tipoDocumentoPagoResponse.codigo) &&
        Objects.equals(this.mensaje, tipoDocumentoPagoResponse.mensaje);
  }

  @Override
  public int hashCode() {
    return Objects.hash(tiposDocumento, codigo, mensaje);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TipoDocumentoPagoResponse {\n");
    
    sb.append("    tiposDocumento: ").append(toIndentedString(tiposDocumento)).append("\n");
    sb.append("    codigo: ").append(toIndentedString(codigo)).append("\n");
    sb.append("    mensaje: ").append(toIndentedString(mensaje)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

