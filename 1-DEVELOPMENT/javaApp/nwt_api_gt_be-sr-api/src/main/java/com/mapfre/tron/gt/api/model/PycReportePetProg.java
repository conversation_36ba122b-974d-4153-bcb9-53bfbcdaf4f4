package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycReportePetProg
 */
@Validated

public class PycReportePetProg   {
  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("tipo")
  private String tipo = null;

  @JsonProperty("nombrePeticion")
  private String nombrePeticion = null;

  @JsonProperty("descPeticion")
  private String descPeticion = null;

  @JsonProperty("fechaCreacion")
  private String fechaCreacion = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("fechaInicio")
  private String fechaInicio = null;

  @JsonProperty("fechaFin")
  private String fechaFin = null;

  @JsonProperty("inicioDesa")
  private String inicioDesa = null;

  @JsonProperty("finDesa")
  private String finDesa = null;

  @JsonProperty("horaDesa")
  private Double horaDesa = null;

  @JsonProperty("inicioAnalisis")
  private String inicioAnalisis = null;

  @JsonProperty("finAnalisis")
  private String finAnalisis = null;

  @JsonProperty("horaAnalisis")
  private Double horaAnalisis = null;

  @JsonProperty("prioridad")
  private String prioridad = null;

  @JsonProperty("usuario")
  private String usuario = null;

  @JsonProperty("analista")
  private String analista = null;

  @JsonProperty("codClarity")
  private String codClarity = null;

  @JsonProperty("nombreArea")
  private String nombreArea = null;

  @JsonProperty("nombreDepartamento")
  private String nombreDepartamento = null;

  @JsonProperty("observaciones")
  private String observaciones = null;

  @JsonProperty("idPeticionSiguiente")
  private Integer idPeticionSiguiente = null;

  @JsonProperty("nomPeticionSiguiente")
  private String nomPeticionSiguiente = null;

  @JsonProperty("diasTotal")
  private Double diasTotal = null;

  @JsonProperty("semanaTotal")
  private Double semanaTotal = null;

  @JsonProperty("diasInhabiles")
  private Double diasInhabiles = null;

  @JsonProperty("diasReales")
  private Double diasReales = null;

  public PycReportePetProg idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador único de la petición
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", value = "Identificador único de la petición")


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycReportePetProg tipo(String tipo) {
    this.tipo = tipo;
    return this;
  }

  /**
   * Descripción del tipo de petición
   * @return tipo
  **/
  @ApiModelProperty(example = "Desarrollo de Software", value = "Descripción del tipo de petición")


  public String getTipo() {
    return tipo;
  }

  public void setTipo(String tipo) {
    this.tipo = tipo;
  }

  public PycReportePetProg nombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
    return this;
  }

  /**
   * Nombre de la petición (en mayúsculas)
   * @return nombrePeticion
  **/
  @ApiModelProperty(example = "NUEVA FUNCIONALIDAD SISTEMA", value = "Nombre de la petición (en mayúsculas)")


  public String getNombrePeticion() {
    return nombrePeticion;
  }

  public void setNombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
  }

  public PycReportePetProg descPeticion(String descPeticion) {
    this.descPeticion = descPeticion;
    return this;
  }

  /**
   * Descripción detallada de la petición (en mayúsculas)
   * @return descPeticion
  **/
  @ApiModelProperty(example = "DESARROLLO DE NUEVA FUNCIONALIDAD PARA EL SISTEMA", value = "Descripción detallada de la petición (en mayúsculas)")


  public String getDescPeticion() {
    return descPeticion;
  }

  public void setDescPeticion(String descPeticion) {
    this.descPeticion = descPeticion;
  }

  public PycReportePetProg fechaCreacion(String fechaCreacion) {
    this.fechaCreacion = fechaCreacion;
    return this;
  }

  /**
   * Fecha de creación de la petición
   * @return fechaCreacion
  **/
  @ApiModelProperty(example = "2024-01-15", value = "Fecha de creación de la petición")


  public String getFechaCreacion() {
    return fechaCreacion;
  }

  public void setFechaCreacion(String fechaCreacion) {
    this.fechaCreacion = fechaCreacion;
  }

  public PycReportePetProg estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Nombre del estado actual de la petición
   * @return estado
  **/
  @ApiModelProperty(example = "En Proceso", value = "Nombre del estado actual de la petición")


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public PycReportePetProg fechaInicio(String fechaInicio) {
    this.fechaInicio = fechaInicio;
    return this;
  }

  /**
   * Fecha de inicio de la petición (formato dd/MM/yyyy)
   * @return fechaInicio
  **/
  @ApiModelProperty(example = "15/01/2024", value = "Fecha de inicio de la petición (formato dd/MM/yyyy)")


  public String getFechaInicio() {
    return fechaInicio;
  }

  public void setFechaInicio(String fechaInicio) {
    this.fechaInicio = fechaInicio;
  }

  public PycReportePetProg fechaFin(String fechaFin) {
    this.fechaFin = fechaFin;
    return this;
  }

  /**
   * Fecha de fin de la petición (formato dd/MM/yyyy)
   * @return fechaFin
  **/
  @ApiModelProperty(example = "28/02/2024", value = "Fecha de fin de la petición (formato dd/MM/yyyy)")


  public String getFechaFin() {
    return fechaFin;
  }

  public void setFechaFin(String fechaFin) {
    this.fechaFin = fechaFin;
  }

  public PycReportePetProg inicioDesa(String inicioDesa) {
    this.inicioDesa = inicioDesa;
    return this;
  }

  /**
   * Fecha de inicio de desarrollo (categoría 3, formato dd/MM/yyyy)
   * @return inicioDesa
  **/
  @ApiModelProperty(example = "20/01/2024", value = "Fecha de inicio de desarrollo (categoría 3, formato dd/MM/yyyy)")


  public String getInicioDesa() {
    return inicioDesa;
  }

  public void setInicioDesa(String inicioDesa) {
    this.inicioDesa = inicioDesa;
  }

  public PycReportePetProg finDesa(String finDesa) {
    this.finDesa = finDesa;
    return this;
  }

  /**
   * Fecha de fin de desarrollo (categoría 3, formato dd/MM/yyyy)
   * @return finDesa
  **/
  @ApiModelProperty(example = "25/02/2024", value = "Fecha de fin de desarrollo (categoría 3, formato dd/MM/yyyy)")


  public String getFinDesa() {
    return finDesa;
  }

  public void setFinDesa(String finDesa) {
    this.finDesa = finDesa;
  }

  public PycReportePetProg horaDesa(Double horaDesa) {
    this.horaDesa = horaDesa;
    return this;
  }

  /**
   * Total de horas reales de desarrollo (categoría 3)
   * @return horaDesa
  **/
  @ApiModelProperty(example = "120.5", value = "Total de horas reales de desarrollo (categoría 3)")


  public Double getHoraDesa() {
    return horaDesa;
  }

  public void setHoraDesa(Double horaDesa) {
    this.horaDesa = horaDesa;
  }

  public PycReportePetProg inicioAnalisis(String inicioAnalisis) {
    this.inicioAnalisis = inicioAnalisis;
    return this;
  }

  /**
   * Fecha de inicio de análisis (categoría 2, formato dd/MM/yyyy)
   * @return inicioAnalisis
  **/
  @ApiModelProperty(example = "16/01/2024", value = "Fecha de inicio de análisis (categoría 2, formato dd/MM/yyyy)")


  public String getInicioAnalisis() {
    return inicioAnalisis;
  }

  public void setInicioAnalisis(String inicioAnalisis) {
    this.inicioAnalisis = inicioAnalisis;
  }

  public PycReportePetProg finAnalisis(String finAnalisis) {
    this.finAnalisis = finAnalisis;
    return this;
  }

  /**
   * Fecha de fin de análisis (categoría 2, formato dd/MM/yyyy)
   * @return finAnalisis
  **/
  @ApiModelProperty(example = "19/01/2024", value = "Fecha de fin de análisis (categoría 2, formato dd/MM/yyyy)")


  public String getFinAnalisis() {
    return finAnalisis;
  }

  public void setFinAnalisis(String finAnalisis) {
    this.finAnalisis = finAnalisis;
  }

  public PycReportePetProg horaAnalisis(Double horaAnalisis) {
    this.horaAnalisis = horaAnalisis;
    return this;
  }

  /**
   * Total de horas base de análisis (categoría 2)
   * @return horaAnalisis
  **/
  @ApiModelProperty(example = "24.0", value = "Total de horas base de análisis (categoría 2)")


  public Double getHoraAnalisis() {
    return horaAnalisis;
  }

  public void setHoraAnalisis(Double horaAnalisis) {
    this.horaAnalisis = horaAnalisis;
  }

  public PycReportePetProg prioridad(String prioridad) {
    this.prioridad = prioridad;
    return this;
  }

  /**
   * Descripción de la prioridad de la petición
   * @return prioridad
  **/
  @ApiModelProperty(example = "Alta", value = "Descripción de la prioridad de la petición")


  public String getPrioridad() {
    return prioridad;
  }

  public void setPrioridad(String prioridad) {
    this.prioridad = prioridad;
  }

  public PycReportePetProg usuario(String usuario) {
    this.usuario = usuario;
    return this;
  }

  /**
   * Nombre completo del usuario solicitante
   * @return usuario
  **/
  @ApiModelProperty(example = "Juan Pérez", value = "Nombre completo del usuario solicitante")


  public String getUsuario() {
    return usuario;
  }

  public void setUsuario(String usuario) {
    this.usuario = usuario;
  }

  public PycReportePetProg analista(String analista) {
    this.analista = analista;
    return this;
  }

  /**
   * Nombre completo del analista asignado
   * @return analista
  **/
  @ApiModelProperty(example = "María García", value = "Nombre completo del analista asignado")


  public String getAnalista() {
    return analista;
  }

  public void setAnalista(String analista) {
    this.analista = analista;
  }

  public PycReportePetProg codClarity(String codClarity) {
    this.codClarity = codClarity;
    return this;
  }

  /**
   * Código de Clarity asociado a la petición
   * @return codClarity
  **/
  @ApiModelProperty(example = "CLR-2024-001", value = "Código de Clarity asociado a la petición")


  public String getCodClarity() {
    return codClarity;
  }

  public void setCodClarity(String codClarity) {
    this.codClarity = codClarity;
  }

  public PycReportePetProg nombreArea(String nombreArea) {
    this.nombreArea = nombreArea;
    return this;
  }

  /**
   * Nombre del área del usuario solicitante
   * @return nombreArea
  **/
  @ApiModelProperty(example = "Tecnología", value = "Nombre del área del usuario solicitante")


  public String getNombreArea() {
    return nombreArea;
  }

  public void setNombreArea(String nombreArea) {
    this.nombreArea = nombreArea;
  }

  public PycReportePetProg nombreDepartamento(String nombreDepartamento) {
    this.nombreDepartamento = nombreDepartamento;
    return this;
  }

  /**
   * Nombre del departamento del usuario solicitante
   * @return nombreDepartamento
  **/
  @ApiModelProperty(example = "Desarrollo de Software", value = "Nombre del departamento del usuario solicitante")


  public String getNombreDepartamento() {
    return nombreDepartamento;
  }

  public void setNombreDepartamento(String nombreDepartamento) {
    this.nombreDepartamento = nombreDepartamento;
  }

  public PycReportePetProg observaciones(String observaciones) {
    this.observaciones = observaciones;
    return this;
  }

  /**
   * Última observación de la petición (en mayúsculas)
   * @return observaciones
  **/
  @ApiModelProperty(example = "PETICIÓN EN PROCESO DE DESARROLLO", value = "Última observación de la petición (en mayúsculas)")


  public String getObservaciones() {
    return observaciones;
  }

  public void setObservaciones(String observaciones) {
    this.observaciones = observaciones;
  }

  public PycReportePetProg idPeticionSiguiente(Integer idPeticionSiguiente) {
    this.idPeticionSiguiente = idPeticionSiguiente;
    return this;
  }

  /**
   * ID de la petición siguiente relacionada
   * @return idPeticionSiguiente
  **/
  @ApiModelProperty(example = "1002", value = "ID de la petición siguiente relacionada")


  public Integer getIdPeticionSiguiente() {
    return idPeticionSiguiente;
  }

  public void setIdPeticionSiguiente(Integer idPeticionSiguiente) {
    this.idPeticionSiguiente = idPeticionSiguiente;
  }

  public PycReportePetProg nomPeticionSiguiente(String nomPeticionSiguiente) {
    this.nomPeticionSiguiente = nomPeticionSiguiente;
    return this;
  }

  /**
   * Nombre de la petición siguiente (en mayúsculas)
   * @return nomPeticionSiguiente
  **/
  @ApiModelProperty(example = "FASE 2 DEL DESARROLLO", value = "Nombre de la petición siguiente (en mayúsculas)")


  public String getNomPeticionSiguiente() {
    return nomPeticionSiguiente;
  }

  public void setNomPeticionSiguiente(String nomPeticionSiguiente) {
    this.nomPeticionSiguiente = nomPeticionSiguiente;
  }

  public PycReportePetProg diasTotal(Double diasTotal) {
    this.diasTotal = diasTotal;
    return this;
  }

  /**
   * Total de días entre fecha inicio y fin
   * @return diasTotal
  **/
  @ApiModelProperty(example = "44.0", value = "Total de días entre fecha inicio y fin")


  public Double getDiasTotal() {
    return diasTotal;
  }

  public void setDiasTotal(Double diasTotal) {
    this.diasTotal = diasTotal;
  }

  public PycReportePetProg semanaTotal(Double semanaTotal) {
    this.semanaTotal = semanaTotal;
    return this;
  }

  /**
   * Total de semanas (días totales / 7)
   * @return semanaTotal
  **/
  @ApiModelProperty(example = "6.29", value = "Total de semanas (días totales / 7)")


  public Double getSemanaTotal() {
    return semanaTotal;
  }

  public void setSemanaTotal(Double semanaTotal) {
    this.semanaTotal = semanaTotal;
  }

  public PycReportePetProg diasInhabiles(Double diasInhabiles) {
    this.diasInhabiles = diasInhabiles;
    return this;
  }

  /**
   * Días inhábiles calculados (semanas * 2)
   * @return diasInhabiles
  **/
  @ApiModelProperty(example = "12.58", value = "Días inhábiles calculados (semanas * 2)")


  public Double getDiasInhabiles() {
    return diasInhabiles;
  }

  public void setDiasInhabiles(Double diasInhabiles) {
    this.diasInhabiles = diasInhabiles;
  }

  public PycReportePetProg diasReales(Double diasReales) {
    this.diasReales = diasReales;
    return this;
  }

  /**
   * Días reales de trabajo (total - inhábiles)
   * @return diasReales
  **/
  @ApiModelProperty(example = "31.42", value = "Días reales de trabajo (total - inhábiles)")


  public Double getDiasReales() {
    return diasReales;
  }

  public void setDiasReales(Double diasReales) {
    this.diasReales = diasReales;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycReportePetProg pycReportePetProg = (PycReportePetProg) o;
    return Objects.equals(this.idPeticion, pycReportePetProg.idPeticion) &&
        Objects.equals(this.tipo, pycReportePetProg.tipo) &&
        Objects.equals(this.nombrePeticion, pycReportePetProg.nombrePeticion) &&
        Objects.equals(this.descPeticion, pycReportePetProg.descPeticion) &&
        Objects.equals(this.fechaCreacion, pycReportePetProg.fechaCreacion) &&
        Objects.equals(this.estado, pycReportePetProg.estado) &&
        Objects.equals(this.fechaInicio, pycReportePetProg.fechaInicio) &&
        Objects.equals(this.fechaFin, pycReportePetProg.fechaFin) &&
        Objects.equals(this.inicioDesa, pycReportePetProg.inicioDesa) &&
        Objects.equals(this.finDesa, pycReportePetProg.finDesa) &&
        Objects.equals(this.horaDesa, pycReportePetProg.horaDesa) &&
        Objects.equals(this.inicioAnalisis, pycReportePetProg.inicioAnalisis) &&
        Objects.equals(this.finAnalisis, pycReportePetProg.finAnalisis) &&
        Objects.equals(this.horaAnalisis, pycReportePetProg.horaAnalisis) &&
        Objects.equals(this.prioridad, pycReportePetProg.prioridad) &&
        Objects.equals(this.usuario, pycReportePetProg.usuario) &&
        Objects.equals(this.analista, pycReportePetProg.analista) &&
        Objects.equals(this.codClarity, pycReportePetProg.codClarity) &&
        Objects.equals(this.nombreArea, pycReportePetProg.nombreArea) &&
        Objects.equals(this.nombreDepartamento, pycReportePetProg.nombreDepartamento) &&
        Objects.equals(this.observaciones, pycReportePetProg.observaciones) &&
        Objects.equals(this.idPeticionSiguiente, pycReportePetProg.idPeticionSiguiente) &&
        Objects.equals(this.nomPeticionSiguiente, pycReportePetProg.nomPeticionSiguiente) &&
        Objects.equals(this.diasTotal, pycReportePetProg.diasTotal) &&
        Objects.equals(this.semanaTotal, pycReportePetProg.semanaTotal) &&
        Objects.equals(this.diasInhabiles, pycReportePetProg.diasInhabiles) &&
        Objects.equals(this.diasReales, pycReportePetProg.diasReales);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, tipo, nombrePeticion, descPeticion, fechaCreacion, estado, fechaInicio, fechaFin, inicioDesa, finDesa, horaDesa, inicioAnalisis, finAnalisis, horaAnalisis, prioridad, usuario, analista, codClarity, nombreArea, nombreDepartamento, observaciones, idPeticionSiguiente, nomPeticionSiguiente, diasTotal, semanaTotal, diasInhabiles, diasReales);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycReportePetProg {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    tipo: ").append(toIndentedString(tipo)).append("\n");
    sb.append("    nombrePeticion: ").append(toIndentedString(nombrePeticion)).append("\n");
    sb.append("    descPeticion: ").append(toIndentedString(descPeticion)).append("\n");
    sb.append("    fechaCreacion: ").append(toIndentedString(fechaCreacion)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    fechaInicio: ").append(toIndentedString(fechaInicio)).append("\n");
    sb.append("    fechaFin: ").append(toIndentedString(fechaFin)).append("\n");
    sb.append("    inicioDesa: ").append(toIndentedString(inicioDesa)).append("\n");
    sb.append("    finDesa: ").append(toIndentedString(finDesa)).append("\n");
    sb.append("    horaDesa: ").append(toIndentedString(horaDesa)).append("\n");
    sb.append("    inicioAnalisis: ").append(toIndentedString(inicioAnalisis)).append("\n");
    sb.append("    finAnalisis: ").append(toIndentedString(finAnalisis)).append("\n");
    sb.append("    horaAnalisis: ").append(toIndentedString(horaAnalisis)).append("\n");
    sb.append("    prioridad: ").append(toIndentedString(prioridad)).append("\n");
    sb.append("    usuario: ").append(toIndentedString(usuario)).append("\n");
    sb.append("    analista: ").append(toIndentedString(analista)).append("\n");
    sb.append("    codClarity: ").append(toIndentedString(codClarity)).append("\n");
    sb.append("    nombreArea: ").append(toIndentedString(nombreArea)).append("\n");
    sb.append("    nombreDepartamento: ").append(toIndentedString(nombreDepartamento)).append("\n");
    sb.append("    observaciones: ").append(toIndentedString(observaciones)).append("\n");
    sb.append("    idPeticionSiguiente: ").append(toIndentedString(idPeticionSiguiente)).append("\n");
    sb.append("    nomPeticionSiguiente: ").append(toIndentedString(nomPeticionSiguiente)).append("\n");
    sb.append("    diasTotal: ").append(toIndentedString(diasTotal)).append("\n");
    sb.append("    semanaTotal: ").append(toIndentedString(semanaTotal)).append("\n");
    sb.append("    diasInhabiles: ").append(toIndentedString(diasInhabiles)).append("\n");
    sb.append("    diasReales: ").append(toIndentedString(diasReales)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

