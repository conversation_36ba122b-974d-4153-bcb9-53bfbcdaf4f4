package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycInsertUsuario
 */
@Validated

public class PycInsertUsuario   {
  @JsonProperty("primerNombre")
  private String primerNombre = null;

  @JsonProperty("segundoNombre")
  private String segundoNombre = null;

  @JsonProperty("primerApellido")
  private String primerApellido = null;

  @JsonProperty("segundoApellido")
  private String segundoApellido = null;

  @JsonProperty("usuarioBaseDatos")
  private String usuarioBaseDatos = null;

  @JsonProperty("email")
  private String email = null;

  @JsonProperty("genero")
  private String genero = null;

  public PycInsertUsuario primerNombre(String primerNombre) {
    this.primerNombre = primerNombre;
    return this;
  }

  /**
   * Primer nombre del usuario
   * @return primerNombre
  **/
  @ApiModelProperty(example = "Juan", required = true, value = "Primer nombre del usuario")
  @NotNull


  public String getPrimerNombre() {
    return primerNombre;
  }

  public void setPrimerNombre(String primerNombre) {
    this.primerNombre = primerNombre;
  }

  public PycInsertUsuario segundoNombre(String segundoNombre) {
    this.segundoNombre = segundoNombre;
    return this;
  }

  /**
   * Segundo nombre del usuario (opcional)
   * @return segundoNombre
  **/
  @ApiModelProperty(example = "Carlos", value = "Segundo nombre del usuario (opcional)")


  public String getSegundoNombre() {
    return segundoNombre;
  }

  public void setSegundoNombre(String segundoNombre) {
    this.segundoNombre = segundoNombre;
  }

  public PycInsertUsuario primerApellido(String primerApellido) {
    this.primerApellido = primerApellido;
    return this;
  }

  /**
   * Primer apellido del usuario
   * @return primerApellido
  **/
  @ApiModelProperty(example = "Pérez", required = true, value = "Primer apellido del usuario")
  @NotNull


  public String getPrimerApellido() {
    return primerApellido;
  }

  public void setPrimerApellido(String primerApellido) {
    this.primerApellido = primerApellido;
  }

  public PycInsertUsuario segundoApellido(String segundoApellido) {
    this.segundoApellido = segundoApellido;
    return this;
  }

  /**
   * Segundo apellido del usuario (opcional)
   * @return segundoApellido
  **/
  @ApiModelProperty(example = "García", value = "Segundo apellido del usuario (opcional)")


  public String getSegundoApellido() {
    return segundoApellido;
  }

  public void setSegundoApellido(String segundoApellido) {
    this.segundoApellido = segundoApellido;
  }

  public PycInsertUsuario usuarioBaseDatos(String usuarioBaseDatos) {
    this.usuarioBaseDatos = usuarioBaseDatos;
    return this;
  }

  /**
   * Usuario de base de datos
   * @return usuarioBaseDatos
  **/
  @ApiModelProperty(example = "jperez", required = true, value = "Usuario de base de datos")
  @NotNull


  public String getUsuarioBaseDatos() {
    return usuarioBaseDatos;
  }

  public void setUsuarioBaseDatos(String usuarioBaseDatos) {
    this.usuarioBaseDatos = usuarioBaseDatos;
  }

  public PycInsertUsuario email(String email) {
    this.email = email;
    return this;
  }

  /**
   * Correo electrónico del usuario
   * @return email
  **/
  @ApiModelProperty(example = "<EMAIL>", required = true, value = "Correo electrónico del usuario")
  @NotNull


  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public PycInsertUsuario genero(String genero) {
    this.genero = genero;
    return this;
  }

  /**
   * Género del usuario (M/F)
   * @return genero
  **/
  @ApiModelProperty(example = "M", required = true, value = "Género del usuario (M/F)")
  @NotNull


  public String getGenero() {
    return genero;
  }

  public void setGenero(String genero) {
    this.genero = genero;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycInsertUsuario pycInsertUsuario = (PycInsertUsuario) o;
    return Objects.equals(this.primerNombre, pycInsertUsuario.primerNombre) &&
        Objects.equals(this.segundoNombre, pycInsertUsuario.segundoNombre) &&
        Objects.equals(this.primerApellido, pycInsertUsuario.primerApellido) &&
        Objects.equals(this.segundoApellido, pycInsertUsuario.segundoApellido) &&
        Objects.equals(this.usuarioBaseDatos, pycInsertUsuario.usuarioBaseDatos) &&
        Objects.equals(this.email, pycInsertUsuario.email) &&
        Objects.equals(this.genero, pycInsertUsuario.genero);
  }

  @Override
  public int hashCode() {
    return Objects.hash(primerNombre, segundoNombre, primerApellido, segundoApellido, usuarioBaseDatos, email, genero);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycInsertUsuario {\n");
    
    sb.append("    primerNombre: ").append(toIndentedString(primerNombre)).append("\n");
    sb.append("    segundoNombre: ").append(toIndentedString(segundoNombre)).append("\n");
    sb.append("    primerApellido: ").append(toIndentedString(primerApellido)).append("\n");
    sb.append("    segundoApellido: ").append(toIndentedString(segundoApellido)).append("\n");
    sb.append("    usuarioBaseDatos: ").append(toIndentedString(usuarioBaseDatos)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("    genero: ").append(toIndentedString(genero)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

