package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycPeticionPerfil
 */
@Validated

public class PycPeticionPerfil   {
  @JsonProperty("idPeticion")
  private Integer idPeticion = null;

  @JsonProperty("nombrePeticion")
  private String nombrePeticion = null;

  @JsonProperty("descripcionPeticion")
  private String descripcionPeticion = null;

  @JsonProperty("idPerfil")
  private Integer idPerfil = null;

  @JsonProperty("nombrePerfil")
  private String nombrePerfil = null;

  @JsonProperty("idProceso")
  private Integer idProceso = null;

  @JsonProperty("nombreProceso")
  private String nombreProceso = null;

  @JsonProperty("idEstado")
  private String idEstado = null;

  @JsonProperty("nombreEstado")
  private String nombreEstado = null;

  @JsonProperty("fechaCreacion")
  private String fechaCreacion = null;

  @JsonProperty("idUsuarioSolicitante")
  private Integer idUsuarioSolicitante = null;

  @JsonProperty("nombreUsuarioSolicitante")
  private String nombreUsuarioSolicitante = null;

  @JsonProperty("idCanal")
  private Integer idCanal = null;

  @JsonProperty("nombreCanal")
  private String nombreCanal = null;

  @JsonProperty("idOficina")
  private Integer idOficina = null;

  @JsonProperty("nombreOficina")
  private String nombreOficina = null;

  @JsonProperty("prioridad")
  private String prioridad = null;

  @JsonProperty("nombreCliente")
  private String nombreCliente = null;

  @JsonProperty("telefonoCliente")
  private String telefonoCliente = null;

  @JsonProperty("correoCliente")
  private String correoCliente = null;

  public PycPeticionPerfil idPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * Identificador de la petición
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", value = "Identificador de la petición")


  public Integer getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(Integer idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycPeticionPerfil nombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
    return this;
  }

  /**
   * Nombre de la petición
   * @return nombrePeticion
  **/
  @ApiModelProperty(example = "Solicitud de cambio de datos", value = "Nombre de la petición")


  public String getNombrePeticion() {
    return nombrePeticion;
  }

  public void setNombrePeticion(String nombrePeticion) {
    this.nombrePeticion = nombrePeticion;
  }

  public PycPeticionPerfil descripcionPeticion(String descripcionPeticion) {
    this.descripcionPeticion = descripcionPeticion;
    return this;
  }

  /**
   * Descripción detallada de la petición
   * @return descripcionPeticion
  **/
  @ApiModelProperty(example = "Solicitud para cambiar datos personales en la póliza", value = "Descripción detallada de la petición")


  public String getDescripcionPeticion() {
    return descripcionPeticion;
  }

  public void setDescripcionPeticion(String descripcionPeticion) {
    this.descripcionPeticion = descripcionPeticion;
  }

  public PycPeticionPerfil idPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
    return this;
  }

  /**
   * Identificador del perfil
   * @return idPerfil
  **/
  @ApiModelProperty(example = "1", value = "Identificador del perfil")


  public Integer getIdPerfil() {
    return idPerfil;
  }

  public void setIdPerfil(Integer idPerfil) {
    this.idPerfil = idPerfil;
  }

  public PycPeticionPerfil nombrePerfil(String nombrePerfil) {
    this.nombrePerfil = nombrePerfil;
    return this;
  }

  /**
   * Nombre del perfil
   * @return nombrePerfil
  **/
  @ApiModelProperty(example = "Administrador", value = "Nombre del perfil")


  public String getNombrePerfil() {
    return nombrePerfil;
  }

  public void setNombrePerfil(String nombrePerfil) {
    this.nombrePerfil = nombrePerfil;
  }

  public PycPeticionPerfil idProceso(Integer idProceso) {
    this.idProceso = idProceso;
    return this;
  }

  /**
   * Identificador del proceso
   * @return idProceso
  **/
  @ApiModelProperty(example = "3", value = "Identificador del proceso")


  public Integer getIdProceso() {
    return idProceso;
  }

  public void setIdProceso(Integer idProceso) {
    this.idProceso = idProceso;
  }

  public PycPeticionPerfil nombreProceso(String nombreProceso) {
    this.nombreProceso = nombreProceso;
    return this;
  }

  /**
   * Nombre del proceso
   * @return nombreProceso
  **/
  @ApiModelProperty(example = "Gestión de Peticiones", value = "Nombre del proceso")


  public String getNombreProceso() {
    return nombreProceso;
  }

  public void setNombreProceso(String nombreProceso) {
    this.nombreProceso = nombreProceso;
  }

  public PycPeticionPerfil idEstado(String idEstado) {
    this.idEstado = idEstado;
    return this;
  }

  /**
   * Identificador del estado
   * @return idEstado
  **/
  @ApiModelProperty(example = "PEN", value = "Identificador del estado")


  public String getIdEstado() {
    return idEstado;
  }

  public void setIdEstado(String idEstado) {
    this.idEstado = idEstado;
  }

  public PycPeticionPerfil nombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
    return this;
  }

  /**
   * Nombre del estado
   * @return nombreEstado
  **/
  @ApiModelProperty(example = "Pendiente", value = "Nombre del estado")


  public String getNombreEstado() {
    return nombreEstado;
  }

  public void setNombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
  }

  public PycPeticionPerfil fechaCreacion(String fechaCreacion) {
    this.fechaCreacion = fechaCreacion;
    return this;
  }

  /**
   * Fecha de creación de la petición
   * @return fechaCreacion
  **/
  @ApiModelProperty(example = "2025-05-21T14:20:00Z", value = "Fecha de creación de la petición")


  public String getFechaCreacion() {
    return fechaCreacion;
  }

  public void setFechaCreacion(String fechaCreacion) {
    this.fechaCreacion = fechaCreacion;
  }

  public PycPeticionPerfil idUsuarioSolicitante(Integer idUsuarioSolicitante) {
    this.idUsuarioSolicitante = idUsuarioSolicitante;
    return this;
  }

  /**
   * Identificador del usuario solicitante
   * @return idUsuarioSolicitante
  **/
  @ApiModelProperty(example = "1234", value = "Identificador del usuario solicitante")


  public Integer getIdUsuarioSolicitante() {
    return idUsuarioSolicitante;
  }

  public void setIdUsuarioSolicitante(Integer idUsuarioSolicitante) {
    this.idUsuarioSolicitante = idUsuarioSolicitante;
  }

  public PycPeticionPerfil nombreUsuarioSolicitante(String nombreUsuarioSolicitante) {
    this.nombreUsuarioSolicitante = nombreUsuarioSolicitante;
    return this;
  }

  /**
   * Nombre del usuario solicitante
   * @return nombreUsuarioSolicitante
  **/
  @ApiModelProperty(example = "Juan Pérez", value = "Nombre del usuario solicitante")


  public String getNombreUsuarioSolicitante() {
    return nombreUsuarioSolicitante;
  }

  public void setNombreUsuarioSolicitante(String nombreUsuarioSolicitante) {
    this.nombreUsuarioSolicitante = nombreUsuarioSolicitante;
  }

  public PycPeticionPerfil idCanal(Integer idCanal) {
    this.idCanal = idCanal;
    return this;
  }

  /**
   * Identificador del canal
   * @return idCanal
  **/
  @ApiModelProperty(example = "1", value = "Identificador del canal")


  public Integer getIdCanal() {
    return idCanal;
  }

  public void setIdCanal(Integer idCanal) {
    this.idCanal = idCanal;
  }

  public PycPeticionPerfil nombreCanal(String nombreCanal) {
    this.nombreCanal = nombreCanal;
    return this;
  }

  /**
   * Nombre del canal
   * @return nombreCanal
  **/
  @ApiModelProperty(example = "Web", value = "Nombre del canal")


  public String getNombreCanal() {
    return nombreCanal;
  }

  public void setNombreCanal(String nombreCanal) {
    this.nombreCanal = nombreCanal;
  }

  public PycPeticionPerfil idOficina(Integer idOficina) {
    this.idOficina = idOficina;
    return this;
  }

  /**
   * Identificador de la oficina
   * @return idOficina
  **/
  @ApiModelProperty(example = "1", value = "Identificador de la oficina")


  public Integer getIdOficina() {
    return idOficina;
  }

  public void setIdOficina(Integer idOficina) {
    this.idOficina = idOficina;
  }

  public PycPeticionPerfil nombreOficina(String nombreOficina) {
    this.nombreOficina = nombreOficina;
    return this;
  }

  /**
   * Nombre de la oficina
   * @return nombreOficina
  **/
  @ApiModelProperty(example = "Oficina Central", value = "Nombre de la oficina")


  public String getNombreOficina() {
    return nombreOficina;
  }

  public void setNombreOficina(String nombreOficina) {
    this.nombreOficina = nombreOficina;
  }

  public PycPeticionPerfil prioridad(String prioridad) {
    this.prioridad = prioridad;
    return this;
  }

  /**
   * Prioridad de la petición
   * @return prioridad
  **/
  @ApiModelProperty(example = "Alta", value = "Prioridad de la petición")


  public String getPrioridad() {
    return prioridad;
  }

  public void setPrioridad(String prioridad) {
    this.prioridad = prioridad;
  }

  public PycPeticionPerfil nombreCliente(String nombreCliente) {
    this.nombreCliente = nombreCliente;
    return this;
  }

  /**
   * Nombre del cliente
   * @return nombreCliente
  **/
  @ApiModelProperty(example = "Juan Carlos Pérez García", value = "Nombre del cliente")


  public String getNombreCliente() {
    return nombreCliente;
  }

  public void setNombreCliente(String nombreCliente) {
    this.nombreCliente = nombreCliente;
  }

  public PycPeticionPerfil telefonoCliente(String telefonoCliente) {
    this.telefonoCliente = telefonoCliente;
    return this;
  }

  /**
   * Teléfono del cliente
   * @return telefonoCliente
  **/
  @ApiModelProperty(example = "22345678", value = "Teléfono del cliente")


  public String getTelefonoCliente() {
    return telefonoCliente;
  }

  public void setTelefonoCliente(String telefonoCliente) {
    this.telefonoCliente = telefonoCliente;
  }

  public PycPeticionPerfil correoCliente(String correoCliente) {
    this.correoCliente = correoCliente;
    return this;
  }

  /**
   * Correo electrónico del cliente
   * @return correoCliente
  **/
  @ApiModelProperty(example = "<EMAIL>", value = "Correo electrónico del cliente")


  public String getCorreoCliente() {
    return correoCliente;
  }

  public void setCorreoCliente(String correoCliente) {
    this.correoCliente = correoCliente;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycPeticionPerfil pycPeticionPerfil = (PycPeticionPerfil) o;
    return Objects.equals(this.idPeticion, pycPeticionPerfil.idPeticion) &&
        Objects.equals(this.nombrePeticion, pycPeticionPerfil.nombrePeticion) &&
        Objects.equals(this.descripcionPeticion, pycPeticionPerfil.descripcionPeticion) &&
        Objects.equals(this.idPerfil, pycPeticionPerfil.idPerfil) &&
        Objects.equals(this.nombrePerfil, pycPeticionPerfil.nombrePerfil) &&
        Objects.equals(this.idProceso, pycPeticionPerfil.idProceso) &&
        Objects.equals(this.nombreProceso, pycPeticionPerfil.nombreProceso) &&
        Objects.equals(this.idEstado, pycPeticionPerfil.idEstado) &&
        Objects.equals(this.nombreEstado, pycPeticionPerfil.nombreEstado) &&
        Objects.equals(this.fechaCreacion, pycPeticionPerfil.fechaCreacion) &&
        Objects.equals(this.idUsuarioSolicitante, pycPeticionPerfil.idUsuarioSolicitante) &&
        Objects.equals(this.nombreUsuarioSolicitante, pycPeticionPerfil.nombreUsuarioSolicitante) &&
        Objects.equals(this.idCanal, pycPeticionPerfil.idCanal) &&
        Objects.equals(this.nombreCanal, pycPeticionPerfil.nombreCanal) &&
        Objects.equals(this.idOficina, pycPeticionPerfil.idOficina) &&
        Objects.equals(this.nombreOficina, pycPeticionPerfil.nombreOficina) &&
        Objects.equals(this.prioridad, pycPeticionPerfil.prioridad) &&
        Objects.equals(this.nombreCliente, pycPeticionPerfil.nombreCliente) &&
        Objects.equals(this.telefonoCliente, pycPeticionPerfil.telefonoCliente) &&
        Objects.equals(this.correoCliente, pycPeticionPerfil.correoCliente);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, nombrePeticion, descripcionPeticion, idPerfil, nombrePerfil, idProceso, nombreProceso, idEstado, nombreEstado, fechaCreacion, idUsuarioSolicitante, nombreUsuarioSolicitante, idCanal, nombreCanal, idOficina, nombreOficina, prioridad, nombreCliente, telefonoCliente, correoCliente);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycPeticionPerfil {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    nombrePeticion: ").append(toIndentedString(nombrePeticion)).append("\n");
    sb.append("    descripcionPeticion: ").append(toIndentedString(descripcionPeticion)).append("\n");
    sb.append("    idPerfil: ").append(toIndentedString(idPerfil)).append("\n");
    sb.append("    nombrePerfil: ").append(toIndentedString(nombrePerfil)).append("\n");
    sb.append("    idProceso: ").append(toIndentedString(idProceso)).append("\n");
    sb.append("    nombreProceso: ").append(toIndentedString(nombreProceso)).append("\n");
    sb.append("    idEstado: ").append(toIndentedString(idEstado)).append("\n");
    sb.append("    nombreEstado: ").append(toIndentedString(nombreEstado)).append("\n");
    sb.append("    fechaCreacion: ").append(toIndentedString(fechaCreacion)).append("\n");
    sb.append("    idUsuarioSolicitante: ").append(toIndentedString(idUsuarioSolicitante)).append("\n");
    sb.append("    nombreUsuarioSolicitante: ").append(toIndentedString(nombreUsuarioSolicitante)).append("\n");
    sb.append("    idCanal: ").append(toIndentedString(idCanal)).append("\n");
    sb.append("    nombreCanal: ").append(toIndentedString(nombreCanal)).append("\n");
    sb.append("    idOficina: ").append(toIndentedString(idOficina)).append("\n");
    sb.append("    nombreOficina: ").append(toIndentedString(nombreOficina)).append("\n");
    sb.append("    prioridad: ").append(toIndentedString(prioridad)).append("\n");
    sb.append("    nombreCliente: ").append(toIndentedString(nombreCliente)).append("\n");
    sb.append("    telefonoCliente: ").append(toIndentedString(telefonoCliente)).append("\n");
    sb.append("    correoCliente: ").append(toIndentedString(correoCliente)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

