package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycUsuario
 */
@Validated

public class PycUsuario   {
  @JsonProperty("idUsuario")
  private Integer idUsuario = null;

  @JsonProperty("nombre")
  private String nombre = null;

  @JsonProperty("fechaCreacion")
  private String fechaCreacion = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("fechaBaja")
  private String fechaBaja = null;

  @JsonProperty("usuarioBaseDatos")
  private String usuarioBaseDatos = null;

  @JsonProperty("email")
  private String email = null;

  @JsonProperty("genero")
  private String genero = null;

  public PycUsuario idUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
    return this;
  }

  /**
   * Identificador del usuario
   * @return idUsuario
  **/
  @ApiModelProperty(example = "1234", value = "Identificador del usuario")


  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public PycUsuario nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Nombre del usuario
   * @return nombre
  **/
  @ApiModelProperty(example = "Juan Pérez", value = "Nombre del usuario")


  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public PycUsuario fechaCreacion(String fechaCreacion) {
    this.fechaCreacion = fechaCreacion;
    return this;
  }

  /**
   * Fecha de creación del usuario
   * @return fechaCreacion
  **/
  @ApiModelProperty(example = "2025-05-21T14:20:00Z", value = "Fecha de creación del usuario")


  public String getFechaCreacion() {
    return fechaCreacion;
  }

  public void setFechaCreacion(String fechaCreacion) {
    this.fechaCreacion = fechaCreacion;
  }

  public PycUsuario estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Estado del usuario
   * @return estado
  **/
  @ApiModelProperty(example = "ACT", value = "Estado del usuario")


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public PycUsuario fechaBaja(String fechaBaja) {
    this.fechaBaja = fechaBaja;
    return this;
  }

  /**
   * Fecha de baja del usuario (si aplica)
   * @return fechaBaja
  **/
  @ApiModelProperty(example = "2025-12-31T00:00:00Z", value = "Fecha de baja del usuario (si aplica)")


  public String getFechaBaja() {
    return fechaBaja;
  }

  public void setFechaBaja(String fechaBaja) {
    this.fechaBaja = fechaBaja;
  }

  public PycUsuario usuarioBaseDatos(String usuarioBaseDatos) {
    this.usuarioBaseDatos = usuarioBaseDatos;
    return this;
  }

  /**
   * Nombre de usuario en la base de datos
   * @return usuarioBaseDatos
  **/
  @ApiModelProperty(example = "jperezdb", value = "Nombre de usuario en la base de datos")


  public String getUsuarioBaseDatos() {
    return usuarioBaseDatos;
  }

  public void setUsuarioBaseDatos(String usuarioBaseDatos) {
    this.usuarioBaseDatos = usuarioBaseDatos;
  }

  public PycUsuario email(String email) {
    this.email = email;
    return this;
  }

  /**
   * Correo electrónico del usuario
   * @return email
  **/
  @ApiModelProperty(example = "<EMAIL>", value = "Correo electrónico del usuario")


  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public PycUsuario genero(String genero) {
    this.genero = genero;
    return this;
  }

  /**
   * Género del usuario
   * @return genero
  **/
  @ApiModelProperty(example = "M", value = "Género del usuario")


  public String getGenero() {
    return genero;
  }

  public void setGenero(String genero) {
    this.genero = genero;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycUsuario pycUsuario = (PycUsuario) o;
    return Objects.equals(this.idUsuario, pycUsuario.idUsuario) &&
        Objects.equals(this.nombre, pycUsuario.nombre) &&
        Objects.equals(this.fechaCreacion, pycUsuario.fechaCreacion) &&
        Objects.equals(this.estado, pycUsuario.estado) &&
        Objects.equals(this.fechaBaja, pycUsuario.fechaBaja) &&
        Objects.equals(this.usuarioBaseDatos, pycUsuario.usuarioBaseDatos) &&
        Objects.equals(this.email, pycUsuario.email) &&
        Objects.equals(this.genero, pycUsuario.genero);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idUsuario, nombre, fechaCreacion, estado, fechaBaja, usuarioBaseDatos, email, genero);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycUsuario {\n");
    
    sb.append("    idUsuario: ").append(toIndentedString(idUsuario)).append("\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("    fechaCreacion: ").append(toIndentedString(fechaCreacion)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    fechaBaja: ").append(toIndentedString(fechaBaja)).append("\n");
    sb.append("    usuarioBaseDatos: ").append(toIndentedString(usuarioBaseDatos)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("    genero: ").append(toIndentedString(genero)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

