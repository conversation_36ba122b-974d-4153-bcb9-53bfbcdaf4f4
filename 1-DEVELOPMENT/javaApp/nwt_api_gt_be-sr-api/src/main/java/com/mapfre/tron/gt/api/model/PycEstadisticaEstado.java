package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycEstadisticaEstado
 */
@Validated

public class PycEstadisticaEstado   {
  @JsonProperty("nombreEstado")
  private String nombreEstado = null;

  @JsonProperty("idEstado")
  private Integer idEstado = null;

  @JsonProperty("colorEstado")
  private String colorEstado = null;

  @JsonProperty("cantidadEstado")
  private Integer cantidadEstado = null;

  @JsonProperty("totalEstado")
  private Integer totalEstado = null;

  @JsonProperty("porcentajeEstado")
  private Double porcentajeEstado = null;

  public PycEstadisticaEstado nombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
    return this;
  }

  /**
   * Nombre descriptivo del estado
   * @return nombreEstado
  **/
  @ApiModelProperty(example = "En Proceso", value = "Nombre descriptivo del estado")


  public String getNombreEstado() {
    return nombreEstado;
  }

  public void setNombreEstado(String nombreEstado) {
    this.nombreEstado = nombreEstado;
  }

  public PycEstadisticaEstado idEstado(Integer idEstado) {
    this.idEstado = idEstado;
    return this;
  }

  /**
   * Identificador único del estado
   * @return idEstado
  **/
  @ApiModelProperty(example = "2", value = "Identificador único del estado")


  public Integer getIdEstado() {
    return idEstado;
  }

  public void setIdEstado(Integer idEstado) {
    this.idEstado = idEstado;
  }

  public PycEstadisticaEstado colorEstado(String colorEstado) {
    this.colorEstado = colorEstado;
    return this;
  }

  /**
   * Color asociado al estado para visualización
   * @return colorEstado
  **/
  @ApiModelProperty(example = "#FFA500", value = "Color asociado al estado para visualización")


  public String getColorEstado() {
    return colorEstado;
  }

  public void setColorEstado(String colorEstado) {
    this.colorEstado = colorEstado;
  }

  public PycEstadisticaEstado cantidadEstado(Integer cantidadEstado) {
    this.cantidadEstado = cantidadEstado;
    return this;
  }

  /**
   * Cantidad de peticiones en este estado
   * @return cantidadEstado
  **/
  @ApiModelProperty(example = "15", value = "Cantidad de peticiones en este estado")


  public Integer getCantidadEstado() {
    return cantidadEstado;
  }

  public void setCantidadEstado(Integer cantidadEstado) {
    this.cantidadEstado = cantidadEstado;
  }

  public PycEstadisticaEstado totalEstado(Integer totalEstado) {
    this.totalEstado = totalEstado;
    return this;
  }

  /**
   * Total de peticiones en todos los estados
   * @return totalEstado
  **/
  @ApiModelProperty(example = "100", value = "Total de peticiones en todos los estados")


  public Integer getTotalEstado() {
    return totalEstado;
  }

  public void setTotalEstado(Integer totalEstado) {
    this.totalEstado = totalEstado;
  }

  public PycEstadisticaEstado porcentajeEstado(Double porcentajeEstado) {
    this.porcentajeEstado = porcentajeEstado;
    return this;
  }

  /**
   * Porcentaje que representa este estado del total (redondeado a 2 decimales)
   * @return porcentajeEstado
  **/
  @ApiModelProperty(example = "15.0", value = "Porcentaje que representa este estado del total (redondeado a 2 decimales)")


  public Double getPorcentajeEstado() {
    return porcentajeEstado;
  }

  public void setPorcentajeEstado(Double porcentajeEstado) {
    this.porcentajeEstado = porcentajeEstado;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycEstadisticaEstado pycEstadisticaEstado = (PycEstadisticaEstado) o;
    return Objects.equals(this.nombreEstado, pycEstadisticaEstado.nombreEstado) &&
        Objects.equals(this.idEstado, pycEstadisticaEstado.idEstado) &&
        Objects.equals(this.colorEstado, pycEstadisticaEstado.colorEstado) &&
        Objects.equals(this.cantidadEstado, pycEstadisticaEstado.cantidadEstado) &&
        Objects.equals(this.totalEstado, pycEstadisticaEstado.totalEstado) &&
        Objects.equals(this.porcentajeEstado, pycEstadisticaEstado.porcentajeEstado);
  }

  @Override
  public int hashCode() {
    return Objects.hash(nombreEstado, idEstado, colorEstado, cantidadEstado, totalEstado, porcentajeEstado);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycEstadisticaEstado {\n");
    
    sb.append("    nombreEstado: ").append(toIndentedString(nombreEstado)).append("\n");
    sb.append("    idEstado: ").append(toIndentedString(idEstado)).append("\n");
    sb.append("    colorEstado: ").append(toIndentedString(colorEstado)).append("\n");
    sb.append("    cantidadEstado: ").append(toIndentedString(cantidadEstado)).append("\n");
    sb.append("    totalEstado: ").append(toIndentedString(totalEstado)).append("\n");
    sb.append("    porcentajeEstado: ").append(toIndentedString(porcentajeEstado)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

