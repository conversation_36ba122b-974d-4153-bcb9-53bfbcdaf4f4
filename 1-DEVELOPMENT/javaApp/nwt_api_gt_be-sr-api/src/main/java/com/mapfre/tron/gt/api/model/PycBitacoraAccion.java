package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycBitacoraAccion
 */
@Validated

public class PycBitacoraAccion   {
  @JsonProperty("idProceso")
  private Integer idProceso = null;

  @JsonProperty("accion")
  private String accion = null;

  @JsonProperty("descripcion")
  private String descripcion = null;

  @JsonProperty("estado")
  private String estado = null;

  @JsonProperty("refEntidad")
  private String refEntidad = null;

  @JsonProperty("refEntidadId")
  private String refEntidadId = null;

  public PycBitacoraAccion idProceso(Integer idProceso) {
    this.idProceso = idProceso;
    return this;
  }

  /**
   * ID del proceso asociado a la acción
   * @return idProceso
  **/
  @ApiModelProperty(example = "7", required = true, value = "ID del proceso asociado a la acción")
  @NotNull


  public Integer getIdProceso() {
    return idProceso;
  }

  public void setIdProceso(Integer idProceso) {
    this.idProceso = idProceso;
  }

  public PycBitacoraAccion accion(String accion) {
    this.accion = accion;
    return this;
  }

  /**
   * Tipo de acción realizada
   * @return accion
  **/
  @ApiModelProperty(example = "INSERT", required = true, value = "Tipo de acción realizada")
  @NotNull


  public String getAccion() {
    return accion;
  }

  public void setAccion(String accion) {
    this.accion = accion;
  }

  public PycBitacoraAccion descripcion(String descripcion) {
    this.descripcion = descripcion;
    return this;
  }

  /**
   * Descripción detallada de la acción
   * @return descripcion
  **/
  @ApiModelProperty(example = "Se creó una nueva petición", required = true, value = "Descripción detallada de la acción")
  @NotNull


  public String getDescripcion() {
    return descripcion;
  }

  public void setDescripcion(String descripcion) {
    this.descripcion = descripcion;
  }

  public PycBitacoraAccion estado(String estado) {
    this.estado = estado;
    return this;
  }

  /**
   * Estado de la acción
   * @return estado
  **/
  @ApiModelProperty(example = "1", required = true, value = "Estado de la acción")
  @NotNull


  public String getEstado() {
    return estado;
  }

  public void setEstado(String estado) {
    this.estado = estado;
  }

  public PycBitacoraAccion refEntidad(String refEntidad) {
    this.refEntidad = refEntidad;
    return this;
  }

  /**
   * Entidad de referencia relacionada con la acción
   * @return refEntidad
  **/
  @ApiModelProperty(example = "PETICION", required = true, value = "Entidad de referencia relacionada con la acción")
  @NotNull


  public String getRefEntidad() {
    return refEntidad;
  }

  public void setRefEntidad(String refEntidad) {
    this.refEntidad = refEntidad;
  }

  public PycBitacoraAccion refEntidadId(String refEntidadId) {
    this.refEntidadId = refEntidadId;
    return this;
  }

  /**
   * ID de la entidad de referencia
   * @return refEntidadId
  **/
  @ApiModelProperty(example = "PET-2024-001", required = true, value = "ID de la entidad de referencia")
  @NotNull


  public String getRefEntidadId() {
    return refEntidadId;
  }

  public void setRefEntidadId(String refEntidadId) {
    this.refEntidadId = refEntidadId;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycBitacoraAccion pycBitacoraAccion = (PycBitacoraAccion) o;
    return Objects.equals(this.idProceso, pycBitacoraAccion.idProceso) &&
        Objects.equals(this.accion, pycBitacoraAccion.accion) &&
        Objects.equals(this.descripcion, pycBitacoraAccion.descripcion) &&
        Objects.equals(this.estado, pycBitacoraAccion.estado) &&
        Objects.equals(this.refEntidad, pycBitacoraAccion.refEntidad) &&
        Objects.equals(this.refEntidadId, pycBitacoraAccion.refEntidadId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idProceso, accion, descripcion, estado, refEntidad, refEntidadId);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycBitacoraAccion {\n");
    
    sb.append("    idProceso: ").append(toIndentedString(idProceso)).append("\n");
    sb.append("    accion: ").append(toIndentedString(accion)).append("\n");
    sb.append("    descripcion: ").append(toIndentedString(descripcion)).append("\n");
    sb.append("    estado: ").append(toIndentedString(estado)).append("\n");
    sb.append("    refEntidad: ").append(toIndentedString(refEntidad)).append("\n");
    sb.append("    refEntidadId: ").append(toIndentedString(refEntidadId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

