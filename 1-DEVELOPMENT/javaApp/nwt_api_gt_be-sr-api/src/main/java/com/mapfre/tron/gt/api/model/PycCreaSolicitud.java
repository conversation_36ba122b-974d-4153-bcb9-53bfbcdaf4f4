package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycCreaSolicitud
 */
@Validated

public class PycCreaSolicitud   {
  @JsonProperty("plataforma")
  private String plataforma = null;

  @JsonProperty("tipoSeguro")
  private String tipoSeguro = null;

  @JsonProperty("nombres")
  private String nombres = null;

  @JsonProperty("apellidos")
  private String apellidos = null;

  @JsonProperty("telefono")
  private String telefono = null;

  @JsonProperty("email")
  private String email = null;

  @JsonProperty("xmlText")
  private String xmlText = null;

  @JsonProperty("codinter")
  private String codinter = null;

  public PycCreaSolicitud plataforma(String plataforma) {
    this.plataforma = plataforma;
    return this;
  }

  /**
   * Plataforma desde la que se crea la solicitud
   * @return plataforma
  **/
  @ApiModelProperty(example = "COTIZADOR", required = true, value = "Plataforma desde la que se crea la solicitud")
  @NotNull


  public String getPlataforma() {
    return plataforma;
  }

  public void setPlataforma(String plataforma) {
    this.plataforma = plataforma;
  }

  public PycCreaSolicitud tipoSeguro(String tipoSeguro) {
    this.tipoSeguro = tipoSeguro;
    return this;
  }

  /**
   * Tipo de seguro solicitado
   * @return tipoSeguro
  **/
  @ApiModelProperty(example = "Automóvil", required = true, value = "Tipo de seguro solicitado")
  @NotNull


  public String getTipoSeguro() {
    return tipoSeguro;
  }

  public void setTipoSeguro(String tipoSeguro) {
    this.tipoSeguro = tipoSeguro;
  }

  public PycCreaSolicitud nombres(String nombres) {
    this.nombres = nombres;
    return this;
  }

  /**
   * Nombres del cliente
   * @return nombres
  **/
  @ApiModelProperty(example = "Juan Carlos", required = true, value = "Nombres del cliente")
  @NotNull


  public String getNombres() {
    return nombres;
  }

  public void setNombres(String nombres) {
    this.nombres = nombres;
  }

  public PycCreaSolicitud apellidos(String apellidos) {
    this.apellidos = apellidos;
    return this;
  }

  /**
   * Apellidos del cliente
   * @return apellidos
  **/
  @ApiModelProperty(example = "Pérez García", required = true, value = "Apellidos del cliente")
  @NotNull


  public String getApellidos() {
    return apellidos;
  }

  public void setApellidos(String apellidos) {
    this.apellidos = apellidos;
  }

  public PycCreaSolicitud telefono(String telefono) {
    this.telefono = telefono;
    return this;
  }

  /**
   * Número de teléfono del cliente
   * @return telefono
  **/
  @ApiModelProperty(example = "55123456", required = true, value = "Número de teléfono del cliente")
  @NotNull


  public String getTelefono() {
    return telefono;
  }

  public void setTelefono(String telefono) {
    this.telefono = telefono;
  }

  public PycCreaSolicitud email(String email) {
    this.email = email;
    return this;
  }

  /**
   * Correo electrónico del cliente
   * @return email
  **/
  @ApiModelProperty(example = "<EMAIL>", required = true, value = "Correo electrónico del cliente")
  @NotNull


  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public PycCreaSolicitud xmlText(String xmlText) {
    this.xmlText = xmlText;
    return this;
  }

  /**
   * Datos adicionales de la solicitud en formato XML. Soporta XML directo o envuelto en CDATA para preservar formato. Ejemplos: '<INFO_PETICION></INFO_PETICION>' o '<![CDATA[<INFO_PETICION>          </INFO_PETICION>]]>'
   * @return xmlText
  **/
  @ApiModelProperty(example = "<INFO_PETICION></INFO_PETICION>", value = "Datos adicionales de la solicitud en formato XML. Soporta XML directo o envuelto en CDATA para preservar formato. Ejemplos: '<INFO_PETICION></INFO_PETICION>' o '<![CDATA[<INFO_PETICION>          </INFO_PETICION>]]>'")


  public String getXmlText() {
    return xmlText;
  }

  public void setXmlText(String xmlText) {
    this.xmlText = xmlText;
  }

  public PycCreaSolicitud codinter(String codinter) {
    this.codinter = codinter;
    return this;
  }

  /**
   * Código de intermediario
   * @return codinter
  **/
  @ApiModelProperty(example = "900000", value = "Código de intermediario")


  public String getCodinter() {
    return codinter;
  }

  public void setCodinter(String codinter) {
    this.codinter = codinter;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycCreaSolicitud pycCreaSolicitud = (PycCreaSolicitud) o;
    return Objects.equals(this.plataforma, pycCreaSolicitud.plataforma) &&
        Objects.equals(this.tipoSeguro, pycCreaSolicitud.tipoSeguro) &&
        Objects.equals(this.nombres, pycCreaSolicitud.nombres) &&
        Objects.equals(this.apellidos, pycCreaSolicitud.apellidos) &&
        Objects.equals(this.telefono, pycCreaSolicitud.telefono) &&
        Objects.equals(this.email, pycCreaSolicitud.email) &&
        Objects.equals(this.xmlText, pycCreaSolicitud.xmlText) &&
        Objects.equals(this.codinter, pycCreaSolicitud.codinter);
  }

  @Override
  public int hashCode() {
    return Objects.hash(plataforma, tipoSeguro, nombres, apellidos, telefono, email, xmlText, codinter);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycCreaSolicitud {\n");
    
    sb.append("    plataforma: ").append(toIndentedString(plataforma)).append("\n");
    sb.append("    tipoSeguro: ").append(toIndentedString(tipoSeguro)).append("\n");
    sb.append("    nombres: ").append(toIndentedString(nombres)).append("\n");
    sb.append("    apellidos: ").append(toIndentedString(apellidos)).append("\n");
    sb.append("    telefono: ").append(toIndentedString(telefono)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("    xmlText: ").append(toIndentedString(xmlText)).append("\n");
    sb.append("    codinter: ").append(toIndentedString(codinter)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

