package com.mapfre.tron.gt.api.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * PycUpdEstadoPet
 */
@Validated

public class PycUpdEstadoPet   {
  @JsonProperty("idPeticion")
  private String idPeticion = null;

  @JsonProperty("nuevoEstado")
  private Integer nuevoEstado = null;

  @JsonProperty("observacion")
  private String observacion = null;

  @JsonProperty("nuevoEstadoApi")
  private String nuevoEstadoApi = null;

  @JsonProperty("motivo")
  private String motivo = null;

  public PycUpdEstadoPet idPeticion(String idPeticion) {
    this.idPeticion = idPeticion;
    return this;
  }

  /**
   * ID de la petición
   * @return idPeticion
  **/
  @ApiModelProperty(example = "1001", required = true, value = "ID de la petición")
  @NotNull


  public String getIdPeticion() {
    return idPeticion;
  }

  public void setIdPeticion(String idPeticion) {
    this.idPeticion = idPeticion;
  }

  public PycUpdEstadoPet nuevoEstado(Integer nuevoEstado) {
    this.nuevoEstado = nuevoEstado;
    return this;
  }

  /**
   * Nuevo estado a asignar a la petición
   * @return nuevoEstado
  **/
  @ApiModelProperty(example = "2", required = true, value = "Nuevo estado a asignar a la petición")
  @NotNull


  public Integer getNuevoEstado() {
    return nuevoEstado;
  }

  public void setNuevoEstado(Integer nuevoEstado) {
    this.nuevoEstado = nuevoEstado;
  }

  public PycUpdEstadoPet observacion(String observacion) {
    this.observacion = observacion;
    return this;
  }

  /**
   * Observación sobre el cambio de estado
   * @return observacion
  **/
  @ApiModelProperty(example = "Petición aprobada y lista para desarrollo", required = true, value = "Observación sobre el cambio de estado")
  @NotNull


  public String getObservacion() {
    return observacion;
  }

  public void setObservacion(String observacion) {
    this.observacion = observacion;
  }

  public PycUpdEstadoPet nuevoEstadoApi(String nuevoEstadoApi) {
    this.nuevoEstadoApi = nuevoEstadoApi;
    return this;
  }

  /**
   * Código equivalente desde API externa (opcional)
   * @return nuevoEstadoApi
  **/
  @ApiModelProperty(example = "1", value = "Código equivalente desde API externa (opcional)")


  public String getNuevoEstadoApi() {
    return nuevoEstadoApi;
  }

  public void setNuevoEstadoApi(String nuevoEstadoApi) {
    this.nuevoEstadoApi = nuevoEstadoApi;
  }

  public PycUpdEstadoPet motivo(String motivo) {
    this.motivo = motivo;
    return this;
  }

  /**
   * Motivo del cambio de estado
   * @return motivo
  **/
  @ApiModelProperty(example = "Se termino la fase", value = "Motivo del cambio de estado")


  public String getMotivo() {
    return motivo;
  }

  public void setMotivo(String motivo) {
    this.motivo = motivo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PycUpdEstadoPet pycUpdEstadoPet = (PycUpdEstadoPet) o;
    return Objects.equals(this.idPeticion, pycUpdEstadoPet.idPeticion) &&
        Objects.equals(this.nuevoEstado, pycUpdEstadoPet.nuevoEstado) &&
        Objects.equals(this.observacion, pycUpdEstadoPet.observacion) &&
        Objects.equals(this.nuevoEstadoApi, pycUpdEstadoPet.nuevoEstadoApi) &&
        Objects.equals(this.motivo, pycUpdEstadoPet.motivo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(idPeticion, nuevoEstado, observacion, nuevoEstadoApi, motivo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PycUpdEstadoPet {\n");
    
    sb.append("    idPeticion: ").append(toIndentedString(idPeticion)).append("\n");
    sb.append("    nuevoEstado: ").append(toIndentedString(nuevoEstado)).append("\n");
    sb.append("    observacion: ").append(toIndentedString(observacion)).append("\n");
    sb.append("    nuevoEstadoApi: ").append(toIndentedString(nuevoEstadoApi)).append("\n");
    sb.append("    motivo: ").append(toIndentedString(motivo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

