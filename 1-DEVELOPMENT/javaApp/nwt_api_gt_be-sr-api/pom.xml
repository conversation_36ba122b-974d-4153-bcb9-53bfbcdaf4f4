<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.mapfre.tron.gt</groupId>
    <artifactId>nwt_api_gt_be.javaApp</artifactId>
    <version>0.0.1-SNAPSHOT</version>
  </parent>

  <artifactId>nwt_api_gt_be-sr-api</artifactId>
  <packaging>jar</packaging>

  <name>${project.artifactId}:${project.version}</name>
  <description>${project.artifactId}:${project.version}</description>

  <properties>
    <cxf-codegen-plugin.version>3.3.5</cxf-codegen-plugin.version>
    <maven-compiler-plugin.excludes.tron21connector>tron21connector/**</maven-compiler-plugin.excludes.tron21connector>
    <maven-compiler-plugin.excludes.gaiasoaexception>
      com.mapfre.dgtp.gaia.core.model.exception.GaiaSoaException</maven-compiler-plugin.excludes.gaiasoaexception>
    <mapfre.swagger.maven.plugin.version>1.0.2</mapfre.swagger.maven.plugin.version>
    <codegen-base-package>com.mapfre.tron.gt.api</codegen-base-package>
    <sonar.projectKey>${project.artifactId}</sonar.projectKey>
  </properties>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifest>
              <addClasspath>true</addClasspath>
            </manifest>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.cxf</groupId>
        <artifactId>cxf-codegen-plugin</artifactId>
        <version>${cxf-codegen-plugin.version}</version>
        <executions>
          <execution>
            <id>generate-sources</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>wsdl2java</goal>
            </goals>
            <configuration>
              <sourceRoot>${project.build.directory}/generated/java</sourceRoot>
              <wsdlRoot>${basedir}/src/main/resources/META-INF/wsdl</wsdlRoot>
            </configuration>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>com.mapfre.dgtp.aaa.arqos.lib</groupId>
            <artifactId>xercesImpl</artifactId>
            <version>2.8.0</version>
          </dependency>
        </dependencies>
      </plugin>
      <!--Compile
      configuration for exclude generated tron21 proxy classes -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <generatedSourcesDirectory>${project.build.directory}/generated/java</generatedSourcesDirectory>
          <excludes>
            <exclude>${maven-compiler-plugin.excludes.tron21connector}</exclude>
            <exclude>${maven-compiler-plugin.excludes.gaiasoaexception}</exclude>
          </excludes>
        </configuration>
      </plugin>

      <plugin>
        <groupId>com.mapfre.dgtp.mapfre-swagger</groupId>
        <artifactId>mapfre-swagger-codegen-maven-plugin</artifactId>
        <version>${mapfre.swagger.maven.plugin.version}</version>
        <configuration>
          <language>spring</language>
          <configOptions>
            <java8>true</java8>
            <interfaceOnly>true</interfaceOnly>
            <sourceFolder>src/main/java</sourceFolder>
            <hideGenerationTimestamp>true</hideGenerationTimestamp>
            <dateLibrary>java8</dateLibrary>
          </configOptions>
          <generateModels>true</generateModels>
          <generateSupportingFiles>false</generateSupportingFiles>
          <output>${project.basedir}/</output>
          <apiPackage>${codegen-base-package}.sr</apiPackage>
          <modelPackage>${codegen-base-package}.model</modelPackage>
          <invokerPackage>${codegen-base-package}.invoker</invokerPackage>
        </configuration>
        <executions>
          <execution>
            <id>gt_api</id>
            <configuration>
              <inputSpec>${project.basedir}/../../swagger/API_EDGE_NEWTRON.yaml</inputSpec>
              <language/>
            </configuration>
            <goals>
              <goal>generate</goal>
            </goals>
          </execution>
          <execution>
            <id>objects_tron</id>
            <configuration>
              <inputSpec>${project.basedir}/../../swagger/API_OBJECTS.yaml</inputSpec>
              <language/>
            </configuration>
            <goals>
              <goal>generate</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.eclipse.m2e</groupId>
          <artifactId>lifecycle-mapping</artifactId>
          <version>1.0.0</version>
          <configuration>
            <lifecycleMappingMetadata>
              <pluginExecutions>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>com.mapfre.dgtp.mapfre-swagger</groupId>
                    <artifactId>mapfre-swagger-codegen-maven-plugin</artifactId>
                    <versionRange>[1.0.0,)</versionRange>
                    <goals>
                      <goal>generate</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <execute>
                      <runOnIncremental>false</runOnIncremental>
                    </execute>
                  </action>
                </pluginExecution>
              </pluginExecutions>
            </lifecycleMappingMetadata>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <dependencies>
  </dependencies>

</project>