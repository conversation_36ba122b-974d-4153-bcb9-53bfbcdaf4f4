package com.mapfre.tron.gt.api.bl;

import com.mapfre.tron.gt.api.model.ValidaEstadoDeducibleResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleFacResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleResponse;
import com.mapfre.tron.gt.api.model.InfoDeducibleResponse;
import com.mapfre.tron.gt.api.model.AvisoPagoPolizaGrupoResponse;

/**
 * Interfaz para operaciones de transacciones de pagos en la capa de negocio.
 *
 * Proporciona métodos para realizar operaciones relacionadas con transacciones de pagos.
 */
public interface IBlTransacPagos {

    /**
     * Valida el estado del deducible para un siniestro específico.
     *
     * @param codCia Código de compañía (obligatorio)
     * @param numSini Número de siniestro (obligatorio)
     * @return Objeto ValidaEstadoDeducibleResponse con la respuesta de la validación
     */
    public ValidaEstadoDeducibleResponse validaEstadoDeducible(Integer codCia, String numSini);

    /**
     * Obtiene el monto del deducible para facturación de un siniestro específico.
     *
     * @param codCia Código de compañía (obligatorio)
     * @param numSini Número de siniestro (obligatorio)
     * @param mcaFactura Marca de factura ('S' o 'N') (obligatorio)
     * @param numCuota Número de cuota (obligatorio)
     * @return Objeto MontoDeducibleFacResponse con los montos calculados
     */
    public MontoDeducibleFacResponse getMontoDeducibleFac(Integer codCia, String numSini, String mcaFactura, Integer numCuota);

    /**
     * Obtiene el monto del deducible de un siniestro específico.
     *
     * @param codCia Código de compañía (obligatorio)
     * @param numSini Número de siniestro (obligatorio)
     * @return Objeto MontoDeducibleResponse con el monto del deducible
     */
    public MontoDeducibleResponse getMontoDeducible(Integer codCia, String numSini);

    /**
     * Obtiene la información de liquidación del deducible.
     *
     * @param codCia Código de compañía (obligatorio)
     * @param numLiq Número de liquidación (obligatorio)
     * @return Objeto InfoDeducibleResponse con la información de liquidación
     */
    public InfoDeducibleResponse getInfoDeducible(Integer codCia, String numLiq);

    /**
     * Obtiene el aviso de pago de póliza grupo.
     *
     * @param codCia Código de compañía (obligatorio)
     * @param numContrato Número de contrato (obligatorio)
     * @param numPolizaGrupo Número de póliza grupo (obligatorio)
     * @return Objeto AvisoPagoPolizaGrupoResponse con la información del aviso de pago
     */
    public AvisoPagoPolizaGrupoResponse getAvisoPagoPolizaGrupo(Integer codCia, Integer numContrato, String numPolizaGrupo);
}
