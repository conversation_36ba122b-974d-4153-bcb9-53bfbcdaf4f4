package com.mapfre.tron.gt.api.bl;

import com.mapfre.tron.gt.api.model.ValidaEstadoDeducibleResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleFacResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleResponse;
import com.mapfre.tron.gt.api.model.InfoDeducibleResponse;
import com.mapfre.tron.gt.api.model.AvisoPagoPolizaGrupoResponse;
import com.mapfre.tron.gt.api.model.ContratosPolizaGrupoResponse;
import com.mapfre.tron.gt.api.model.ResponsablePagoResponse;
import com.mapfre.tron.gt.api.model.TipoDocumentoPagoResponse;
import java.util.List;

/**
 * Interfaz para operaciones de transacciones de pagos en la capa de negocio.
 *
 * Proporciona métodos para realizar operaciones relacionadas con transacciones de pagos.
 */
public interface IBlTransacPagos {

    /**
     * Valida el estado del deducible para un siniestro específico.
     *
     * @param codCia Código de compañía (obligatorio)
     * @param numSini Número de siniestro (obligatorio)
     * @return Objeto ValidaEstadoDeducibleResponse con la respuesta de la validación
     */
    public ValidaEstadoDeducibleResponse validaEstadoDeducible(Integer codCia, String numSini);

    /**
     * Obtiene el monto del deducible para facturación de un siniestro específico.
     *
     * @param codCia Código de compañía (obligatorio)
     * @param numSini Número de siniestro (obligatorio)
     * @param mcaFactura Marca de factura ('S' o 'N') (obligatorio)
     * @param numCuota Número de cuota (obligatorio)
     * @return Objeto MontoDeducibleFacResponse con los montos calculados
     */
    public MontoDeducibleFacResponse getMontoDeducibleFac(Integer codCia, String numSini, String mcaFactura, Integer numCuota);

    /**
     * Obtiene el monto del deducible de un siniestro específico.
     *
     * @param codCia Código de compañía (obligatorio)
     * @param numSini Número de siniestro (obligatorio)
     * @return Objeto MontoDeducibleResponse con el monto del deducible
     */
    public MontoDeducibleResponse getMontoDeducible(Integer codCia, String numSini);

    /**
     * Obtiene la información de liquidación del deducible.
     *
     * @param codCia Código de compañía (obligatorio)
     * @param numLiq Número de liquidación (obligatorio)
     * @return Objeto InfoDeducibleResponse con la información de liquidación
     */
    public InfoDeducibleResponse getInfoDeducible(Integer codCia, String numLiq);

    /**
     * Obtiene los avisos de pago de póliza grupo.
     *
     * @param codCia Código de compañía (obligatorio)
     * @param numContrato Número de contrato (opcional)
     * @param numPolizaGrupo Número de póliza grupo (obligatorio)
     * @return Lista de objetos AvisoPagoPolizaGrupoResponse con la información de los avisos de pago
     */
    public List<AvisoPagoPolizaGrupoResponse> getAvisoPagoPolizaGrupo(Integer codCia, Integer numContrato, String numPolizaGrupo);

    /**
     * Valida los tipos de documentos de pago disponibles según el requerimiento.
     *
     * @param requerimiento Código de requerimiento para filtrar los tipos de documentos (obligatorio)
     * @return Respuesta con la lista de tipos de documentos de pago
     */
    public TipoDocumentoPagoResponse validaTipoDocumento(String requerimiento);

    /**
     * Obtiene los contratos de póliza grupo.
     *
     * @param codCia Código de compañía (obligatorio)
     * @param numPolizaGrupo Número de póliza grupo (obligatorio)
     * @return Lista de objetos ContratosPolizaGrupoResponse con la información de los contratos
     */
    public List<ContratosPolizaGrupoResponse> getContratosPolizaGrupo(Integer codCia, String numPolizaGrupo);

    /**
     * Obtiene el responsable de pago.
     *
     * @param codCia Código de compañía (obligatorio)
     * @param tipDocum Tipo de documento (obligatorio)
     * @param codDocum Código de documento (obligatorio)
     * @param codActTercero Código de actividad del tercero (opcional)
     * @return Objeto ResponsablePagoResponse con la información del responsable de pago
     */
    public ResponsablePagoResponse getResponsablePago(Integer codCia, String tipDocum, String codDocum, Integer codActTercero);
}
