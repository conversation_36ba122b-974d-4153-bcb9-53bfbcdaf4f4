package com.mapfre.tron.gt.api.dl;

import java.util.List;

import com.mapfre.tron.gt.api.model.*;

/**
 * Interfaz para operaciones de Pycges en la capa de acceso a datos.
 *
 * Proporciona métodos para interactuar con funcionalidades de Pycges
 * en la base de datos.
 */
public interface IDlPycges {

    /**
     * Obtiene la lista de usuarios.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_USUARIO.
     *
     * @return Lista de objetos PycUsuario con la información de los usuarios
     */
    public List<PycUsuario> getUsuarios();

    /**
     * Obtiene la lista de aplicaciones asociadas a un proceso.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_GET_APLICACION.
     *
     * @param idProceso ID del proceso
     * @return Lista de objetos PycAplicacion con la información de las aplicaciones
     */
    public List<PycAplicacion> getAplicaciones(Integer idProceso);

    /**
     * Obtiene la lista de procesos asociados a un tipo de petición.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_PROCESO_POR_TIPO.
     *
     * @param idTipo ID del tipo de petición
     * @return Lista de objetos PycProcesoPorTipo con la información de los procesos
     */
    public List<PycProcesoPorTipo> getProcesosPorTipo(Integer idTipo);

    /**
     * Obtiene la lista de perfiles activos.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_PERFIL.
     *
     * @return Lista de objetos PycPerfil con la información de los perfiles
     */
    public List<PycPerfil> getPerfiles();

    /**
     * Obtiene la lista de usuarios asociados a un perfil.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_USUARIO_PERFIL.
     *
     * @param idPerfil ID del perfil (opcional, si es null devuelve todos los usuarios con sus perfiles)
     * @return Lista de objetos PycUsuarioPerfil con la información de los usuarios y sus perfiles
     */
    public List<PycUsuarioPerfil> getUsuariosPerfil(Integer idPerfil);

    /**
     * Obtiene el listado de asignaciones por perfil.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_LISTADO_ASIGNA.
     *
     * @param idPerfil ID del perfil
     * @return Lista de objetos PycListadoAsigna con la información de los usuarios asignados al perfil
     */
    public List<PycListadoAsigna> getListadoAsigna(Integer idPerfil);

    /**
     * Obtiene la consulta SQL asociada a un dato variable.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_GET_QUERY_DAT_VAR.
     *
     * @param idProceso ID del proceso
     * @param idSeccion ID de la sección
     * @param idDatoVar ID del dato variable
     * @return Objeto PycGetQueryDatVar con la consulta SQL
     */
    public PycGetQueryDatVar getQueryDatVar(Integer idProceso, Integer idSeccion, Integer idDatoVar);

    /**
     * Verifica si un perfil tiene la opción de selección múltiple.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_SRC_MULTIPLE_OPTION.
     *
     * @param idPerfil ID del perfil
     * @return Objeto PycSrcMultipleOption con el resultado de la verificación
     */
    public PycSrcMultipleOption getSrcMultipleOption(Integer idPerfil);

    /**
     * Obtiene la lista de usuarios asignados según proceso, perfil, aplicación y estado.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_GET_USUARIOS_ASIG.
     *
     * @param idProceso ID del proceso
     * @param idPerfil ID del perfil
     * @param idAplicacion ID de la aplicación
     * @param idEstado ID del estado
     * @return Lista de objetos PycGetUsuariosAsig con la información de los usuarios asignados
     */
    public List<PycGetUsuariosAsig> getUsuariosAsig(Integer idProceso, Integer idPerfil, Integer idAplicacion, Integer idEstado);

    /**
     * Obtiene la lista de usuarios asociados a un perfil y proceso específicos.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_USU_PERFIL_PROCESO.
     *
     * @param idPerfil ID del perfil
     * @param idProceso ID del proceso
     * @return Lista de objetos PycUsuPerfilProceso con la información de los usuarios
     */
    public List<PycUsuPerfilProceso> getUsuPerfilProceso(Integer idPerfil, Integer idProceso);

    /**
     * Obtiene la lista de procesos asociados a un usuario específico.
     * Utiliza la función TRON2000_GT.GC_K_PYC_AUTH_MGT.FN_USUARIO_PROCESOS.
     *
     * @param usuario Nombre de usuario en la base de datos
     * @return Lista de objetos PycUsuProcesos con la información de los procesos del usuario
     */
    public List<PycUsuProcesos> getUsuarioProcesos(String usuario);

    /**
     * Actualiza o crea un registro de perfil para un usuario específico.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.F_MOD_PERFIL.
     *
     * @param perfilData Datos del perfil a actualizar o crear
     * @return ID del usuario actualizado o creado
     */
    public Integer updatePerfil(PycUpdPerfil perfilData);

    /**
     * Actualiza o crea un registro de usuario con sus datos personales y departamento.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.F_MOD_USUARIO.
     *
     * @param usuarioData Datos del usuario a actualizar o crear
     * @return ID del usuario actualizado o creado
     */
    public Integer updateUsuario(PycUpdUsuario usuarioData);

    /**
     * Crea una nueva solicitud en el sistema PYCGES.
     * Utiliza el procedimiento TRON2000_GT.GC_K_PYC_COMERCIAL_MGT.P_CREA_SOLICITUD.
     *
     * @param solicitudData Datos de la solicitud a crear
     * @return ID de la solicitud creada
     */
    public Integer creaSolicitud(PycCreaSolicitud solicitudData);

    /**
     * Inserta una nueva petición en el sistema PYCGES.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_INSERT_PETICION.
     *
     * @param peticionData Datos de la petición a insertar
     * @return ID de la petición creada
     */
    public Integer insertPeticion(PycInsertPeticion peticionData);

    /**
     * Obtiene la lista de peticiones filtradas por perfil, proceso, estado y otros criterios.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_PETICION_PERFIL.
     *
     * @param idPerfil ID del perfil
     * @param idProceso ID del proceso
     * @param idEstado ID del estado
     * @param idPeticion ID de la petición (opcional)
     * @param idCanal ID del canal (opcional)
     * @param idUsuario ID del usuario (opcional)
     * @param indSubs Indicador de substitución (opcional)
     * @param idOficina ID de la oficina (opcional)
     * @return Lista de objetos PycPeticionPerfil con la información de las peticiones
     */
    public List<PycPeticionPerfil> getPeticionPerfil(Integer idPerfil, Integer idProceso, String idEstado,
                                                     Integer idPeticion, Integer idCanal, Integer idUsuario,
                                                     String indSubs, Integer idOficina);

    /**
     * Obtiene la lista de peticiones que no tienen programador asignado.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_PETICION_SIN_PROGRAMADOR.
     *
     * @return Lista de objetos PycPetSinProgramador con la información de las peticiones sin programador
     */
    public List<PycPetSinProgramador> getPeticionSinProgramador();

    /**
     * Obtiene la lista de áreas que tienen usuarios con peticiones asociadas.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_CON_AREA.
     *
     * @return Lista de objetos PycAreaPeti con la información de las áreas con peticiones
     */
    public List<PycAreaPeti> getConArea();

    /**
     * Obtiene la lista de departamentos que tienen usuarios con peticiones, filtrados por área.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_CON_DEPARTAMENTO.
     *
     * @param idArea ID del área (opcional)
     * @return Lista de objetos PycDepartamento con la información de los departamentos
     */
    public List<PycDepartamento> getConDepartamento(Integer idArea);

    /**
     * Obtiene la lista de usuarios que han realizado peticiones, filtrados por área y/o departamento.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_CON_SOLICITANTE.
     *
     * @param idArea ID del área (opcional)
     * @param idDepartamento ID del departamento (opcional)
     * @return Lista de objetos PycAreaUsuario con la información de los usuarios solicitantes
     */
    public List<PycAreaUsuario> getConSolicitante(Integer idArea, Integer idDepartamento);

    /**
     * Obtiene la lista de analistas disponibles filtrados por solicitante y prioridad de petición.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_CON_ANALISTA.
     *
     * @param idSolicitante ID del usuario solicitante
     * @param prioridad Prioridad de la petición (opcional)
     * @return Lista de objetos PycAnalista con la información de los analistas
     */
    public List<PycAnalista> getConAnalista(Integer idSolicitante, String prioridad);

    /**
     * Obtiene la lista de estados de peticiones filtrados por solicitante, prioridad y analista.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_CON_ESTADO.
     *
     * @param idSolicitante ID del usuario solicitante
     * @param prioridad Prioridad de la petición (usar 'null' para peticiones sin prioridad)
     * @param idAnalista ID del analista asignado (opcional)
     * @return Lista de objetos PycConEstado con la información de los estados
     */
    public List<PycConEstado> getConEstado(Integer idSolicitante, String prioridad, Integer idAnalista);

    /**
     * Obtiene la lista de prioridades de peticiones filtradas por solicitante, área y departamento.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_CON_PRIORIDAD.
     *
     * @param idSolicitante ID del usuario solicitante (opcional)
     * @param idArea ID del área (opcional)
     * @param idDepartamento ID del departamento (opcional)
     * @return Lista de objetos PycPrioridad con la información de las prioridades
     */
    public List<PycPrioridad> getConPrioridad(Integer idSolicitante, Integer idArea, Integer idDepartamento);

    /**
     * Obtiene la lista de años distintos de las peticiones filtrados por proceso.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_TAB_ANIOS.
     *
     * @param idProceso ID del proceso
     * @return Lista de objetos PycTabAnios con los años disponibles
     */
    public List<PycTabAnios> getTabAnios(Integer idProceso);

    /**
     * Obtiene la lista de áreas distintas de las peticiones filtradas por proceso y año.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_TAB_AREA.
     *
     * @param idProceso ID del proceso
     * @param anio Año de filtro (formato YYYY) (opcional)
     * @return Lista de objetos PycTabArea con las áreas disponibles
     */
    public List<PycTabArea> getTabArea(Integer idProceso, String anio);

    /**
     * Obtiene la lista de estados distintos de las peticiones filtradas por proceso, año y área.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_TAB_ESTADO.
     *
     * @param idProceso ID del proceso
     * @param anio Año de filtro (formato YYYY) (opcional)
     * @param idArea ID del área (opcional)
     * @return Lista de objetos PycTabEstado con los estados disponibles
     */
    public List<PycTabEstado> getTabEstado(Integer idProceso, String anio, Integer idArea);

    /**
     * Obtiene la lista de usuarios asociados a un perfil específico para una petición y aplicación determinada.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_USUARIOS_PERFIL_PETICION.
     *
     * @param idPeticion ID de la petición
     * @param idPerfil ID del perfil
     * @param idAplicacion ID de la aplicación
     * @return Lista de objetos PycUsuPerfilPeti con la información de los usuarios
     */
    public List<PycUsuPerfilPeti> getUsuariosPerfilPeticion(Integer idPeticion, Integer idPerfil, Integer idAplicacion);

    /**
     * Obtiene la lista de peticiones filtradas por múltiples criterios.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_PETICION_FILTROS.
     *
     * @param idArea ID del área (opcional)
     * @param idDepartamento ID del departamento (opcional)
     * @param idPerfil ID del perfil (opcional)
     * @param idEstado ID del estado (opcional)
     * @param idUsuarioSolicitante ID del usuario solicitante (opcional)
     * @param idTipo ID del tipo de petición (opcional)
     * @param fechaInicio Fecha de inicio del filtro (opcional)
     * @param fechaFin Fecha de fin del filtro (opcional)
     * @param idProceso ID del proceso (opcional)
     * @return Lista de objetos PycPetiFiltro con la información de las peticiones filtradas
     */
    public List<PycPetiFiltro> getPeticionFiltros(Integer idArea, Integer idDepartamento, Integer idPerfil,
                                                  Integer idEstado, Integer idUsuarioSolicitante, Integer idTipo,
                                                  String fechaInicio, String fechaFin, Integer idProceso);

    /**
     * Obtiene el reporte de peticiones filtrado por múltiples criterios.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_REPORTE_PETICION.
     *
     * @param idArea ID del área (opcional)
     * @param idDepartamento ID del departamento (opcional)
     * @param idSolicitante ID del usuario solicitante (opcional)
     * @param prioridad Prioridad de la petición (opcional, usar 'null' para peticiones sin prioridad)
     * @param idAnalista ID del analista asignado (opcional)
     * @param estado IDs de estados separados por coma (opcional)
     * @return Lista de objetos PycReportePeti con la información del reporte de peticiones
     */
    public List<PycReportePeti> getReportePeticion(Integer idArea, Integer idDepartamento, Integer idSolicitante,
                                                   String prioridad, Integer idAnalista, String estado);

    /**
     * Inserta o actualiza la relación entre un usuario y un proceso.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_INSERT_USERPRO.
     *
     * @param userData Datos del usuario proceso a insertar/actualizar
     * @return ID del usuario procesado
     */
    public Integer insertUserProceso(PycInsertUserPro userData);

    /**
     * Inserta un nuevo usuario en el sistema PYCGES.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_INSERT_USUARIO.
     *
     * @param usuarioData Datos del usuario a insertar
     * @return ID del usuario creado
     */
    public Integer insertUsuario(PycInsertUsuario usuarioData);

    /**
     * Actualiza la información de un documento asociado a una petición.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_ACT_DOCUMENTO.
     *
     * @param documentoData Datos del documento a actualizar
     * @return ID de la petición actualizada
     */
    public Integer actDocumento(PycActDocumento documentoData);

    /**
     * Obtiene la lista de observaciones asociadas a una petición específica.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_OBSER_PETICION.
     *
     * @param idPeticion ID de la petición
     * @return Lista de observaciones de la petición
     */
    public List<PycObserPet> getObserPeticion(Integer idPeticion);

    /**
     * Obtiene el resumen estadístico de actividades de una petición específica por categoría.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_RESUMEN_ACTIVIDAD.
     *
     * @param idPeticion ID de la petición
     * @param idCategoria ID de la categoría del tablero
     * @return Lista con el resumen de actividades
     */
    public List<PycResumenActi> getResumenActividad(Integer idPeticion, Integer idCategoria);

    /**
     * Obtiene el avance detallado de peticiones con información de observaciones, usuarios y estados.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_PETICION_AVANCE.
     *
     * @param idProceso ID del proceso (opcional)
     * @param idArea ID del área (opcional)
     * @param estado IDs de estados separados por coma (opcional)
     * @param codcia Código de compañía (opcional)
     * @param anio Año de filtro (opcional)
     * @return Lista de objetos PycPetAvance con el avance de peticiones
     */
    public List<PycPetAvance> getPeticionAvance(Integer idProceso, Integer idArea, String estado, String codcia, Integer anio);

    /**
     * Obtiene la lista de peticiones con información de su petición siguiente asociada.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_PETICION_AVANCE_SIGUIENTE.
     *
     * @param idProceso ID del proceso (opcional)
     * @param idArea ID del área (opcional)
     * @param estado Lista de estados separados por comas (opcional)
     * @param codcia Código de compañía (opcional)
     * @param anio Año de filtro (opcional)
     * @return Lista de objetos PycPetAvanceSig con la información de peticiones y sus siguientes
     */
    public List<PycPetAvanceSig> getPeticionAvanceSiguiente(Integer idProceso, Integer idArea, String estado, String codcia, Integer anio);

    /**
     * Obtiene la lista de valores activos filtrados por tipo y proceso.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_LISTA_VAL.
     *
     * @param tipo Tipo de valor a consultar
     * @param idProceso ID del proceso
     * @return Lista de objetos PycListVal con los valores y descripciones
     */
    public List<PycListVal> getListaVal(String tipo, Integer idProceso);

    /**
     * Obtiene la lista de oficinas disponibles para un usuario específico en un proceso determinado.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.F_GET_OFICINAS.
     * La función considera el indicador de oficina del usuario para determinar si retorna
     * todas las oficinas del proceso o solo las asignadas al usuario.
     *
     * @param idProceso ID del proceso
     * @param idUsuario ID del usuario
     * @return Lista de objetos PycOficina con las oficinas disponibles
     */
    public List<PycOficina> getOficinas(Integer idProceso, Integer idUsuario);

    /**
     * Obtiene la lista de canales disponibles para un usuario específico en un proceso y oficina determinados.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.F_GET_CANALES.
     * La función implementa lógica condicional compleja basada en los indicadores ind_canal e ind_oficina:
     * - Si ind_canal = 'T': Retorna todos los canales activos del proceso para la oficina especificada
     * - Si ind_canal != 'T' AND ind_oficina = 'T': Retorna solo canales asignados al usuario para el proceso
     * - Si ind_canal != 'T' AND ind_oficina != 'T': Retorna solo canales asignados al usuario para la oficina y proceso específicos
     *
     * @param idProceso ID del proceso
     * @param idUsuario ID del usuario
     * @param idOficina ID de la oficina
     * @return Lista de objetos PycCanal con los canales disponibles
     */
    public List<PycCanal> getCanales(Integer idProceso, Integer idUsuario, Integer idOficina);

    /**
     * Obtiene la lista de subordinados disponibles para un supervisor específico considerando canal, oficina, indicador de login y proceso.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.F_GET_SUBORDINADOS.
     * La función implementa lógica condicional extremadamente compleja basada en múltiples indicadores (ind_admin, ind_oficina, ind_canal):
     * - Validaciones iniciales: Si indLogin = 'S' obtiene id_proceso del canal y ind_admin del usuario
     * - Obtiene ind_oficina e ind_canal del supervisor para el proceso
     * - ESCENARIO 1: ind_admin = 'S' OR ind_oficina = 'T' (Supervisor con privilegios administrativos)
     *   - SUB-ESCENARIO 1A: ind_canal = 'T' → Todos los subordinados de la oficina (incluye ID quemado 21)
     *   - SUB-ESCENARIO 1B: ind_canal != 'T' → Subordinados de la oficina filtrados por canal específico
     * - ESCENARIO 2: ind_admin != 'S' AND ind_oficina != 'T' (Supervisor con acceso restringido)
     *   - SUB-ESCENARIO 2A: ind_canal = 'T' → Subordinados directos del supervisor en la oficina
     *   - SUB-ESCENARIO 2B: ind_canal != 'T' → Subordinados directos filtrados por canal y oficina específicos
     * - FALLBACK: Si no se encuentran subordinados, retorna al propio supervisor como único resultado
     *
     * @param canal ID del canal
     * @param oficina ID de la oficina
     * @param idSupervisor ID del supervisor
     * @param indLogin Indicador de login (S/N)
     * @param proceso ID del proceso
     * @return Lista de objetos PycSubordinados con los subordinados disponibles
     */
    public List<PycSubordinados> getSubordinados(String canal, String oficina, String idSupervisor, String indLogin, String proceso);

    /**
     * Obtiene la información completa de un usuario específico incluyendo datos personales, área, departamento, perfil y configuraciones.
     * Utiliza la función TRON2000_GT.GC_K_PYC_AUTH_MGT.FN_INFO_USUARIO.
     * La función realiza múltiples joins entre tablas de usuario, área, departamento, perfil y proceso para obtener información detallada:
     * - Datos básicos del usuario: nombres, apellidos, email, género, fechas, estado
     * - Información organizacional: área y departamento del usuario
     * - Perfiles y procesos: múltiples perfiles con indicador de perfil por defecto
     * - Configuraciones: URLs de perfil e inicio
     * - Filtros: solo registros activos en todas las tablas relacionadas (excepto usuario)
     * - Búsqueda: case-insensitive por nombre de usuario en la base de datos
     * - Ordenamiento: por IND_DEFAULT DESC (perfil por defecto primero)
     *
     * @param usuario Nombre de usuario en la base de datos
     * @return Lista de objetos PycInfoUsuario con la información completa del usuario
     */
    public List<PycInfoUsuario> getInfoUsuario(String usuario);

    /**
     * Obtiene la información de un ramo específico basado en el código de ramo, modalidad y compañía.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_OBTENER_RAMO.
     * La función consulta la tabla PYC_TIPO_PETICION con parámetros que tienen valores por defecto:
     * - P_COD_RAMO: Código del ramo (obligatorio)
     * - P_COD_MODALIDAD: Código de modalidad (opcional, valor por defecto '999')
     * - P_COD_CIA: Código de compañía (opcional, valor por defecto '2')
     * - Consulta directa en tabla PYC_TIPO_PETICION sin joins complejos
     * - Filtros específicos por COD_RAMO, COD_MODALIDAD y COD_CIA
     * - Retorna información del tipo de petición asociado al ramo
     *
     * @param codRamo Código del ramo (obligatorio)
     * @param codModalidad Código de modalidad (opcional, default '999')
     * @param codCia Código de compañía (opcional, default '2')
     * @return Lista de objetos PycRamo con la información del ramo
     */
    public List<PycRamo> obtenerRamo(String codRamo, Integer codModalidad, String codCia);

    /**
     * Obtiene los datos de variables equivalentes basado en el código de ramo, modalidad y compañía.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_OBTENER_DAT_VAR_EQUIV.
     * La función consulta la tabla PYC_DATO_VAR_EQUIV con parámetros que tienen valores por defecto y filtros avanzados:
     * - P_COD_RAMO: Código del ramo (obligatorio, se usa con LIKE para búsquedas flexibles)
     * - P_COD_MODALIDAD: Código de modalidad (opcional, valor por defecto '999')
     * - P_COD_CIA: Código de compañía (opcional, valor por defecto '2')
     * - Filtros especiales: LIKE en COD_RAMO, NVL para valores por defecto, ESTADO = 'ACT'
     * - Consulta directa en tabla PYC_DATO_VAR_EQUIV sin joins complejos
     * - Retorna mapeo entre variables PYC y REEF para formularios y secciones específicas
     *
     * @param codRamo Código del ramo (obligatorio, se usa con LIKE)
     * @param codModalidad Código de modalidad (opcional, default '999')
     * @param codCia Código de compañía (opcional, default '2')
     * @return Lista de objetos PycDatoVarEquiv con los datos de variables equivalentes
     */
    public List<PycDatoVarEquiv> obtenerDatVarEquiv(String codRamo, Integer codModalidad, String codCia);

    /**
     * Obtiene los perfiles asignados a un usuario específico para un proceso determinado.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_PERFILES_USUARIO.
     * La función realiza un JOIN entre las tablas PYC_USUARIO_PERFIL y PYC_PERFIL con filtros específicos:
     * - pIdUsuario: ID del usuario (obligatorio, se convierte a UPPER)
     * - pIdProceso: ID del proceso (obligatorio)
     * - JOIN: A.ID_PERFIL = B.ID_PERFIL entre PYC_USUARIO_PERFIL (A) y PYC_PERFIL (B)
     * - Filtros: Solo registros activos (ESTADO = 'ACT') en ambas tablas
     * - Filtros específicos: Por ID_PROCESO e ID_USUARIO
     * - Ordenamiento: Por A.ORDEN y B.NOMBRE_PERFIL
     * - Retorna información completa del perfil y su asignación al usuario
     *
     * @param idUsuario ID del usuario (obligatorio)
     * @param idProceso ID del proceso (obligatorio)
     * @return Lista de objetos PycPerfilUsu con los perfiles del usuario para el proceso
     */
    public List<PycPerfilUsu> perfilesUsuario(Integer idUsuario, Integer idProceso);

    /**
     * Obtiene la lista de categorías de actividades asociadas a una petición específica con estadísticas de avance.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_OBTENER_CATE_X_PET.
     * La función realiza consultas complejas con múltiples JOINs y subconsultas para obtener estadísticas detalladas:
     * - Consulta principal: JOIN entre PYC_ACTIVIDAD_PETICION y PYC_CATEGORIA_TABLERO
     * - Subconsulta interna: Calcula porcentajes de actividad y avance por categoría
     * - LEFT JOINs: Para actividades terminadas (TER) y activas (ACT)
     * - Agregaciones: COUNT, SUM, MIN, MAX para estadísticas por categoría
     * - Cálculos complejos: Porcentajes de avance basados en estado y horas
     * - Filtros: Por ID_PETICION específico
     * - Agrupamiento: Por petición y categoría
     * - Ordenamiento: Por ID_PETICION e ID_CATEGORIA_TABLERO
     *
     * @param idPeticion ID de la petición (obligatorio)
     * @return Lista de objetos PycCategoriaPet con las categorías y estadísticas de la petición
     */
    public List<PycCategoriaPet> getCategoriaPeticion(Integer idPeticion);

    /**
     * Obtiene la lista completa de peticiones con información básica para mostrar en el tablero.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_OBTENER_PETICIONES_TABLERO.
     * La función realiza JOINs entre las tablas principales del sistema de peticiones:
     * - PYC_PETICION: Información básica de la petición (ID, nombre)
     * - PYC_TIPO_PETICION: Descripción del tipo de petición
     * - PYC_USUARIO: Información del usuario solicitante (primer nombre)
     * - PYC_ESTADO: Nombre del estado actual de la petición
     * - Ordenamiento: Por ID_PETICION DESC (peticiones más recientes primero)
     * - Sin filtros: Retorna todas las peticiones del sistema
     *
     * @return Lista de objetos PycPeticionTab con la información básica de todas las peticiones
     */
    public List<PycPeticionTab> getPeticionesTablero();

    /**
     * Obtiene la lista jerárquica de opciones de menú disponibles para un perfil específico en un proceso determinado.
     * Utiliza la función TRON2000_GT.GC_K_PYC_AUTH_MGT.FN_OPCION_USUARIO.
     * La función realiza consultas complejas con múltiples JOINs y estructura jerárquica:
     * - PYC_OPCION: Información básica de las opciones de menú (ID, nombre, logo, ruta, funciones, estado)
     * - PYC_OPCION_PERFIL: Relación entre opciones y perfiles con orden y permisos
     * - PYC_USUARIO_PERFIL: Asignación de perfiles a usuarios con multiperfil
     * - PYC_PROCESO_OPCION: Personalización de opciones por proceso
     * - Estructura jerárquica: START WITH ID_PADRE = 0 CONNECT BY PRIOR ID_OPCION = ID_PADRE
     * - Filtros: Solo registros activos, usuario actual (USER), perfil y proceso específicos
     * - Permisos: Considera ADMIN según MULTIPERFIL del usuario
     * - Ordenamiento: Por ORDEN definido en PYC_OPCION_PERFIL
     *
     * @param idPerfil ID del perfil (obligatorio)
     * @param idProceso ID del proceso (obligatorio)
     * @return Lista de objetos PycOpcionUsu con las opciones de menú jerárquicas del usuario
     */
    public List<PycOpcionUsu> getOpcionUsuario(Integer idPerfil, Integer idProceso);

    /**
     * Obtiene un reporte detallado de peticiones con información de programación filtradas por año y proceso.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_REPORTE_PETICION_PROG.
     * La función realiza consultas complejas con múltiples JOINs y subconsultas:
     * - PYC_PETICION: Información básica de peticiones (ID, nombre, descripción, fechas, estado)
     * - PYC_TIPO_PETICION: Descripción del tipo de petición
     * - PYC_ESTADO: Nombre del estado actual
     * - PYC_ACTIVIDAD_PETICION: Fechas y horas de desarrollo (categoría 3) y análisis (categoría 2)
     * - PYC_LIST_VAL: Descripción de prioridades por proceso
     * - PYC_USUARIO: Información de solicitantes y analistas
     * - PYC_ANALISTA_PETICION: Asignación de analistas
     * - PYC_OBSERVACION_PETICION: Última observación de cada petición
     * - Área y Departamento: Información organizacional del solicitante
     * - Cálculos: Días totales, semanas, días inhábiles y días reales de trabajo
     * - Filtros: Año de creación y proceso específico
     * - Subconsultas: MIN/MAX fechas, SUM horas por categoría de actividad
     *
     * @param anio Año de filtro para las peticiones (obligatorio)
     * @param idProceso ID del proceso (obligatorio)
     * @return Lista de objetos PycReportePetProg con el reporte detallado de peticiones
     */
    public List<PycReportePetProg> getReportePeticionProgramacion(Integer anio, Integer idProceso);

    /**
     * Obtiene estadísticas agrupadas por estado de peticiones con conteos, totales y porcentajes.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_ESTADISTICA_ESTADOS.
     * La función construye dinámicamente una consulta SQL con múltiples JOINs y agregaciones:
     * - PYC_PETICION: Información básica de peticiones (ID, estado, fecha creación, proceso, usuario solicitante)
     * - PYC_ESTADO: Información del estado (ID, nombre, color)
     * - PYC_USUARIO_DEPARTAMENTO: Relación usuario-departamento-área para filtrado organizacional
     * - Agregaciones: COUNT(*) por estado, SUM(COUNT(*)) OVER() para total, cálculo de porcentajes
     * - Filtros dinámicos: Proceso, área, año de creación, estados específicos (lista), código de compañía
     * - Ordenamiento: Por PORCENTAJE_ESTADO DESC (estados con mayor porcentaje primero)
     * - Query dinámica: Construye SQL con concatenación de strings y condiciones condicionales
     *
     * @param proceso ID del proceso (obligatorio)
     * @param area ID del área (obligatorio)
     * @param anio Año de filtro (obligatorio)
     * @param estado Lista de IDs de estados separados por coma (opcional, ej. "1,2,3")
     * @param codCia Código de compañía (opcional, puede ser null)
     * @return Lista de objetos PycEstadisticaEstado con las estadísticas por estado
     */
    public List<PycEstadisticaEstado> getEstadisticaEstados(Integer proceso, Integer area, Integer anio, String estado, String codCia);

    /**
     * Obtiene estadísticas agrupadas por estado de peticiones siguientes con conteos, totales y porcentajes.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_ESTADISTICA_ESTADOS_SIGU.
     * La función construye dinámicamente una consulta SQL con múltiples JOINs y agregaciones:
     * - PYC_PETICION A: Información básica de peticiones principales (ID, estado, fecha creación, proceso, usuario solicitante)
     * - PYC_PETICION S: Información de peticiones siguientes (ID_PETICION_SIGUIENTE, estado)
     * - PYC_ESTADO: Información del estado de las peticiones siguientes (ID, nombre, color)
     * - PYC_USUARIO_DEPARTAMENTO: Relación usuario-departamento-área para filtrado organizacional
     * - Agregaciones: COUNT(*) por estado siguiente, SUM(COUNT(*)) OVER() para total, cálculo de porcentajes
     * - Filtros dinámicos: Proceso, área, año de creación, estados de petición principal (lista), código de compañía
     * - Ordenamiento: Por PORCENTAJE_ESTADO DESC (estados con mayor porcentaje primero)
     * - Query dinámica: Construye SQL con concatenación de strings y condiciones condicionales
     * - Relación: A.ID_PETICION_SIGUIENTE = S.ID_PETICION para obtener estadísticas de peticiones siguientes
     *
     * @param proceso ID del proceso (obligatorio)
     * @param area ID del área (obligatorio)
     * @param anio Año de filtro (obligatorio)
     * @param estado Lista de IDs de estados de petición principal separados por coma (opcional, ej. "1,2,3")
     * @param codCia Código de compañía (opcional, puede ser null)
     * @return Lista de objetos PycEstadistEstadoSig con las estadísticas por estado de peticiones siguientes
     */
    public List<PycEstadistEstadoSig> getEstadisticaEstadosSiguientes(Integer proceso, Integer area, Integer anio, String estado, String codCia);

    /**
     * Actualiza el estado de una o múltiples peticiones en el sistema PYCGES.
     * Utiliza la función TRON2000_GT.GC_K_PYC_PETICIONES_MGT.FN_UPDATE_PETICION_ESTADO.
     * La función maneja lógica compleja para actualización de estados con validaciones y transiciones:
     * - Soporte para múltiples peticiones: Acepta IDs separados por coma
     * - Validación de transiciones: Verifica estados válidos según proceso y perfil
     * - Equivalencias de estado: Mapea estados de API externa a estados internos
     * - Asignación automática: Asigna perfil según el nuevo estado
     * - Observaciones: Registra observaciones del cambio con complementos automáticos
     * - Programas de transición: Ejecuta programas específicos según la transición
     * - Actualización de solicitudes: Maneja indicadores especiales de actualización
     * - Transacciones: Confirma cambios con COMMIT automático
     *
     * @param estadoData Datos para actualizar el estado de la petición
     * @return ID de la petición procesada (último ID en caso de múltiples peticiones)
     */
    public Integer updateEstadoPeticion(PycUpdEstadoPet estadoData);

    /**
     * Actualiza la URL y PATH del perfil de un usuario específico.
     * Utiliza la función TRON2000_GT.GC_K_PYC_AUTH_MGT.FN_ACTUALIZA_PERFIL.
     * La función actualiza los campos URL_PERFIL y PATH_PERFIL en la tabla TRC_GT_DL.PYC_USUARIO
     * para el usuario especificado y confirma los cambios con COMMIT automático.
     *
     * @param perfilData Datos para actualizar el path y URL del perfil del usuario
     * @return Resultado de la operación (1=éxito, 0=error)
     */
    public Integer updatePathPerfil(PycUpdPathPerfil perfilData);

    /**
     * Actualiza las fechas de login/logout de un usuario específico según el estado de la sesión.
     * Utiliza la función TRON2000_GT.GC_K_PYC_AUTH_MGT.FN_ACTUALIZA_SESION.
     * La función actualiza los campos FECHA_LOGIN y FECHA_LOGOUT en la tabla TRC_GT_DL.PYC_USUARIO:
     * - Si estado = 1 (login): actualiza FECHA_LOGIN = SYSDATE, mantiene FECHA_LOGOUT
     * - Si estado = 0 (logout): mantiene FECHA_LOGIN, actualiza FECHA_LOGOUT = SYSDATE
     *
     * @param sesionData Datos para actualizar la sesión del usuario
     * @return Resultado de la operación (1=éxito, 0=error)
     */
    public Integer updateSesion(PycUpdSesion sesionData);

    /**
     * Registra una acción específica en la bitácora del sistema PYCGES.
     * Utiliza el procedimiento TRON2000_GT.GC_K_PYC_AUTH_MGT.P_BITACORA_ACCION.
     * El procedimiento es autónomo y registra automáticamente información de auditoría:
     * - MAQUINA_HOST, MAQUINA_OS, MAQUINA_IP (del contexto del sistema)
     * - FECHA (SYSDATE) y USUARIO (USER)
     * - Genera ID automático con secuencia SQ_PYC_BITACORA_ACCION
     *
     * @param bitacoraData Datos de la acción a registrar en la bitácora
     * @return true si la operación se ejecutó correctamente, false en caso de error
     */
    public boolean bitacoraAccion(PycBitacoraAccion bitacoraData);
}
