package com.mapfre.tron.gt.api.bl;

import com.mapfre.tron.gt.api.bl.Services.ITasaCambioService;
import com.mapfre.tron.gt.api.dl.IDlCommons;
import com.mapfre.tron.gt.api.model.Message;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.mapfre.tron.gt.api.sr.CacheApi.log;

@Service
@Slf4j
public class BlCommonsImpl extends BaseService implements IBlCommons {

    @Autowired
    private IDlCommons iDlCommons;

    @Autowired
    private ITasaCambioService tasaCambioService;

    @Override
    public Message actualizacionDeTasaDeCambio() {
        log.info("The actualizacionDeTasaDeCambio service has been called!");

        Message response = new Message();
        Date fechaHoy = new Date();
        Date fechaEquivalente = new Date();

        try {
            resetSession();

            double tasaCambio = tasaCambioService.getTipoCambioDia();

            if (tasaCambio <= 0) {
                log.warn("No se pudo obtener una tasa de cambio válida.");
                response.setCode("002");
                response.setTitle("Tasa inválida");
                response.setMessage("No se pudo actualizar la tasa de cambio porque el valor obtenido no es válido.");
                response.setType("warning");
                return response;
            }

            iDlCommons.actualizacionDeTasaDeCambio(fechaHoy, tasaCambio);
            iDlCommons.actualizacionFechaEquivalente(fechaEquivalente);

            response.setCode("200");
            response.setTitle("Actualización exitosa");
            response.setMessage("La tasa de cambio y la fecha equivalente fueron actualizadas correctamente.");
            response.setType("success");

        } catch (Exception e) {
            log.error("Error inesperado en actualizacionDeTasaDeCambio: {}", e.getMessage(), e);
            response.setCode("500");
            response.setTitle("Error inesperado");
            response.setMessage("Ocurrió un error en : " + e.getMessage());
            response.setType("error");
        }

        return response;
    }
}