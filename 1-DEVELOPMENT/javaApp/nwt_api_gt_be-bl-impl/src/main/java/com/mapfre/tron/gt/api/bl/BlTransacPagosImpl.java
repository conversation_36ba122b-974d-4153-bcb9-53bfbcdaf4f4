package com.mapfre.tron.gt.api.bl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapfre.tron.gt.api.dl.IDlTransacPagos;
import com.mapfre.tron.gt.api.model.ValidaEstadoDeducibleResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleFacResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleResponse;
import com.mapfre.tron.gt.api.model.InfoDeducibleResponse;
import com.mapfre.tron.gt.api.model.AvisoPagoPolizaGrupoResponse;

import lombok.extern.slf4j.Slf4j;

import static com.mapfre.tron.gt.api.sr.CacheApi.log;

/**
 * Implementación de la interfaz IBlTransacPagos para operaciones de transacciones de pagos en la capa de negocio.
 */
@Service
@Slf4j
public class BlTransacPagosImpl extends BaseService implements IBlTransacPagos {

    @Autowired
    private IDlTransacPagos iDlTransacPagos;

    @Override
    public ValidaEstadoDeducibleResponse validaEstadoDeducible(Integer codCia, String numSini) {
        log.info("Iniciando validación de estado de deducible con codCia={}, numSini={}", codCia, numSini);

        try {
            // Llamar al servicio de acceso a datos
            ValidaEstadoDeducibleResponse response = iDlTransacPagos.validaEstadoDeducible(codCia, numSini);

            log.info("Validación de estado de deducible completada exitosamente para numSini={}", numSini);
            return response;

        } catch (Exception e) {
            log.error("Error al validar estado de deducible para numSini={}: {}", numSini, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public MontoDeducibleFacResponse getMontoDeducibleFac(Integer codCia, String numSini, String mcaFactura, Integer numCuota) {
        log.info("Iniciando obtención de monto deducible para facturación con codCia={}, numSini={}, mcaFactura={}, numCuota={}",
                 codCia, numSini, mcaFactura, numCuota);

        try {
            // Llamar al servicio de acceso a datos
            MontoDeducibleFacResponse response = iDlTransacPagos.getMontoDeducibleFac(codCia, numSini, mcaFactura, numCuota);

            log.info("Obtención de monto deducible para facturación completada exitosamente para numSini={}", numSini);
            return response;

        } catch (Exception e) {
            log.error("Error al obtener monto deducible para facturación para numSini={}: {}", numSini, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public MontoDeducibleResponse getMontoDeducible(Integer codCia, String numSini) {
        log.info("Iniciando obtención de monto deducible con codCia={}, numSini={}", codCia, numSini);

        try {
            // Llamar al servicio de acceso a datos
            MontoDeducibleResponse response = iDlTransacPagos.getMontoDeducible(codCia, numSini);

            log.info("Obtención de monto deducible completada exitosamente para numSini={}", numSini);
            return response;

        } catch (Exception e) {
            log.error("Error al obtener monto deducible para numSini={}: {}", numSini, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public InfoDeducibleResponse getInfoDeducible(Integer codCia, String numLiq) {
        log.info("Iniciando obtención de información de deducible con codCia={}, numLiq={}", codCia, numLiq);

        try {
            // Llamar al servicio de acceso a datos
            InfoDeducibleResponse response = iDlTransacPagos.getInfoDeducible(codCia, numLiq);

            log.info("Obtención de información de deducible completada exitosamente para numLiq={}", numLiq);
            return response;

        } catch (Exception e) {
            log.error("Error al obtener información de deducible para numLiq={}: {}", numLiq, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public AvisoPagoPolizaGrupoResponse getAvisoPagoPolizaGrupo(Integer codCia, Integer numContrato, String numPolizaGrupo) {
        log.info("Iniciando obtención de aviso de pago de póliza grupo con codCia={}, numContrato={}, numPolizaGrupo={}",
                 codCia, numContrato, numPolizaGrupo);

        try {
            // Llamar al servicio de acceso a datos
            AvisoPagoPolizaGrupoResponse response = iDlTransacPagos.getAvisoPagoPolizaGrupo(codCia, numContrato, numPolizaGrupo);

            log.info("Obtención de aviso de pago de póliza grupo completada exitosamente para numContrato={}", numContrato);
            return response;

        } catch (Exception e) {
            log.error("Error al obtener aviso de pago de póliza grupo para numContrato={}: {}", numContrato, e.getMessage(), e);
            throw e;
        }
    }
}
