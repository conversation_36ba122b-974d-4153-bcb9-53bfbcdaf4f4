package com.mapfre.tron.gt.api.bl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapfre.tron.gt.api.dl.IDlTransacPagos;
import com.mapfre.tron.gt.api.model.ValidaEstadoDeducibleResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleFacResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleResponse;
import com.mapfre.tron.gt.api.model.InfoDeducibleResponse;
import com.mapfre.tron.gt.api.model.AvisoPagoPolizaGrupoResponse;
import com.mapfre.tron.gt.api.model.TipoDocumentoPagoResponse;
import com.mapfre.tron.gt.api.model.TipoDocumentoPago;
import com.mapfre.tron.gt.api.commons.DatabaseConstants;
import java.util.ArrayList;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

import static com.mapfre.tron.gt.api.sr.CacheApi.log;

/**
 * Implementación de la interfaz IBlTransacPagos para operaciones de transacciones de pagos en la capa de negocio.
 */
@Service
@Slf4j
public class BlTransacPagosImpl extends BaseService implements IBlTransacPagos {

    @Autowired
    private IDlTransacPagos iDlTransacPagos;

    @Override
    public ValidaEstadoDeducibleResponse validaEstadoDeducible(Integer codCia, String numSini) {
        log.info("Iniciando validación de estado de deducible con codCia={}, numSini={}", codCia, numSini);

        try {
            // Llamar al servicio de acceso a datos
            ValidaEstadoDeducibleResponse response = iDlTransacPagos.validaEstadoDeducible(codCia, numSini);

            log.info("Validación de estado de deducible completada exitosamente para numSini={}", numSini);
            return response;

        } catch (Exception e) {
            log.error("Error al validar estado de deducible para numSini={}: {}", numSini, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public MontoDeducibleFacResponse getMontoDeducibleFac(Integer codCia, String numSini, String mcaFactura, Integer numCuota) {
        log.info("Iniciando obtención de monto deducible para facturación con codCia={}, numSini={}, mcaFactura={}, numCuota={}",
                 codCia, numSini, mcaFactura, numCuota);

        try {
            // Llamar al servicio de acceso a datos
            MontoDeducibleFacResponse response = iDlTransacPagos.getMontoDeducibleFac(codCia, numSini, mcaFactura, numCuota);

            log.info("Obtención de monto deducible para facturación completada exitosamente para numSini={}", numSini);
            return response;

        } catch (Exception e) {
            log.error("Error al obtener monto deducible para facturación para numSini={}: {}", numSini, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public MontoDeducibleResponse getMontoDeducible(Integer codCia, String numSini) {
        log.info("Iniciando obtención de monto deducible con codCia={}, numSini={}", codCia, numSini);

        try {
            // Llamar al servicio de acceso a datos
            MontoDeducibleResponse response = iDlTransacPagos.getMontoDeducible(codCia, numSini);

            log.info("Obtención de monto deducible completada exitosamente para numSini={}", numSini);
            return response;

        } catch (Exception e) {
            log.error("Error al obtener monto deducible para numSini={}: {}", numSini, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public InfoDeducibleResponse getInfoDeducible(Integer codCia, String numLiq) {
        log.info("Iniciando obtención de información de deducible con codCia={}, numLiq={}", codCia, numLiq);

        try {
            // Llamar al servicio de acceso a datos
            InfoDeducibleResponse response = iDlTransacPagos.getInfoDeducible(codCia, numLiq);

            log.info("Obtención de información de deducible completada exitosamente para numLiq={}", numLiq);
            return response;

        } catch (Exception e) {
            log.error("Error al obtener información de deducible para numLiq={}: {}", numLiq, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<AvisoPagoPolizaGrupoResponse> getAvisoPagoPolizaGrupo(Integer codCia, Integer numContrato, String numPolizaGrupo) {
        log.info("Iniciando obtención de avisos de pago de póliza grupo con codCia={}, numContrato={}, numPolizaGrupo={}",
                 codCia, numContrato, numPolizaGrupo);

        try {
            // Llamar al servicio de acceso a datos
            List<AvisoPagoPolizaGrupoResponse> response = iDlTransacPagos.getAvisoPagoPolizaGrupo(codCia, numContrato, numPolizaGrupo);

            log.info("Obtención de avisos de pago de póliza grupo completada exitosamente para numContrato={}. Registros encontrados: {}",
                     numContrato, response.size());
            return response;

        } catch (Exception e) {
            log.error("Error al obtener avisos de pago de póliza grupo para numContrato={}: {}", numContrato, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public TipoDocumentoPagoResponse validaTipoDocumento(String requerimiento) {
        log.info("Iniciando validación de tipos de documentos de pago para el requerimiento: {}", requerimiento);

        TipoDocumentoPagoResponse response = new TipoDocumentoPagoResponse();

        try {
            // Resetear la sesión antes de la operación
            resetSession();

            // Validar parámetros de entrada
            if (requerimiento == null || requerimiento.trim().isEmpty()) {
                response.setCodigo("400");
                response.setMensaje("El código de requerimiento es obligatorio");
                return response;
            }

            // Llamar a la capa de datos para ejecutar la función
            List<TipoDocumentoPago> tiposDocumento = iDlTransacPagos.validaTipoDocumentoPago(
                    DatabaseConstants.COD_CIA, requerimiento);

            // Procesar los resultados
            if (tiposDocumento != null && !tiposDocumento.isEmpty()) {
                response.setTiposDocumento(tiposDocumento);
                response.setCodigo("200");
                response.setMensaje("Operación exitosa");
            } else {
                response.setTiposDocumento(new ArrayList<>());
                response.setCodigo("204");
                response.setMensaje("No se encontraron tipos de documentos de pago para el requerimiento especificado");
            }

        } catch (Exception e) {
            log.error("Error en validaTipoDocumento: {}", e.getMessage(), e);
            response.setCodigo("500");
            response.setMensaje("Error al procesar la solicitud: " + e.getMessage());
        }

        return response;
    }
}
