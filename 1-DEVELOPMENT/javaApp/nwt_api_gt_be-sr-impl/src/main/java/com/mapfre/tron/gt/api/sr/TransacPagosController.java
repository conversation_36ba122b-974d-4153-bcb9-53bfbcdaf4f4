package com.mapfre.tron.gt.api.sr;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import com.mapfre.tron.gt.api.bl.IBlTransacPagos;
import com.mapfre.tron.gt.api.model.ValidaEstadoDeducibleResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleFacResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleResponse;
import com.mapfre.tron.gt.api.model.InfoDeducibleResponse;
import com.mapfre.tron.gt.api.model.AvisoPagoPolizaGrupoResponse;
import com.mapfre.tron.gt.api.model.TipoDocumentoPagoResponse;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 * Controlador REST para operaciones de transacciones de pagos.
 */
@RestController
@Slf4j
@Api(tags = {"TransacPagos"})
public class TransacPagosController implements TransacPagosApi {

    @Autowired
    private IBlTransacPagos blTransacPagos;

    @Override
    public ResponseEntity<ValidaEstadoDeducibleResponse> validaEstadoDeducible(Integer codCia, String numSini) {
        log.info("Recibida solicitud para validar estado de deducible con codCia={}, numSini={}", codCia, numSini);

        try {
            // Llamar al servicio de negocio
            ValidaEstadoDeducibleResponse response = blTransacPagos.validaEstadoDeducible(codCia, numSini);

            // Verificar si se obtuvo una respuesta válida
            if (response == null || response.getResp() == null || response.getResp().isEmpty()) {
                log.warn("No se obtuvo respuesta válida para la validación del deducible");
                return new ResponseEntity<>(response, HttpStatus.NO_CONTENT);
            }

            log.info("Validación de estado de deducible completada exitosamente para numSini={}", numSini);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al validar estado de deducible para numSini={}: {}", numSini, e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<MontoDeducibleFacResponse> getMontoDeducibleFac(Integer codCia, String numSini, String mcaFactura, Integer numCuota) {
        log.info("Recibida solicitud para obtener monto deducible para facturación con codCia={}, numSini={}, mcaFactura={}, numCuota={}",
                 codCia, numSini, mcaFactura, numCuota);

        try {
            // Llamar al servicio de negocio
            MontoDeducibleFacResponse response = blTransacPagos.getMontoDeducibleFac(codCia, numSini, mcaFactura, numCuota);

            // Verificar si se obtuvo una respuesta válida
            if (response == null) {
                log.warn("No se obtuvo respuesta válida para el cálculo del monto deducible");
                return new ResponseEntity<>(response, HttpStatus.NO_CONTENT);
            }

            log.info("Cálculo de monto deducible para facturación completado exitosamente para numSini={}", numSini);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al obtener monto deducible para facturación para numSini={}: {}", numSini, e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<MontoDeducibleResponse> getMontoDeducible(Integer codCia, String numSini) {
        log.info("Recibida solicitud para obtener monto deducible con codCia={}, numSini={}", codCia, numSini);

        try {
            // Llamar al servicio de negocio
            MontoDeducibleResponse response = blTransacPagos.getMontoDeducible(codCia, numSini);

            // Verificar si se obtuvo una respuesta válida
            if (response == null) {
                log.warn("No se obtuvo respuesta válida para el cálculo del monto deducible");
                return new ResponseEntity<>(response, HttpStatus.NO_CONTENT);
            }

            log.info("Cálculo de monto deducible completado exitosamente para numSini={}", numSini);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al obtener monto deducible para numSini={}: {}", numSini, e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<InfoDeducibleResponse> getInfoDeducible(Integer codCia, String numLiq) {
        log.info("Recibida solicitud para obtener información de deducible con codCia={}, numLiq={}", codCia, numLiq);

        try {
            // Llamar al servicio de negocio
            InfoDeducibleResponse response = blTransacPagos.getInfoDeducible(codCia, numLiq);

            // Verificar si se obtuvo una respuesta válida
            if (response == null) {
                log.warn("No se obtuvo respuesta válida para la información del deducible");
                return new ResponseEntity<>(response, HttpStatus.NO_CONTENT);
            }

            log.info("Obtención de información de deducible completada exitosamente para numLiq={}", numLiq);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al obtener información de deducible para numLiq={}: {}", numLiq, e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<AvisoPagoPolizaGrupoResponse> getAvisoPagoPolizaGrupo(Integer codCia, Integer numContrato, String numPolizaGrupo) {
        log.info("Recibida solicitud para obtener aviso de pago de póliza grupo con codCia={}, numContrato={}, numPolizaGrupo={}",
                 codCia, numContrato, numPolizaGrupo);

        try {
            // Llamar al servicio de negocio
            AvisoPagoPolizaGrupoResponse response = blTransacPagos.getAvisoPagoPolizaGrupo(codCia, numContrato, numPolizaGrupo);

            // Verificar si se obtuvo una respuesta válida
            if (response == null) {
                log.warn("No se obtuvo respuesta válida para el aviso de pago de póliza grupo");
                return new ResponseEntity<>(response, HttpStatus.NO_CONTENT);
            }

            log.info("Obtención de aviso de pago de póliza grupo completada exitosamente para numContrato={}", numContrato);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al obtener aviso de pago de póliza grupo para numContrato={}: {}", numContrato, e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<TipoDocumentoPagoResponse> validaTipoDocumento(String codDocto) {
        log.info("Recibida solicitud para validar tipo de documento con codDocto={}", codDocto);

        try {
            // Llamar al servicio de negocio
            TipoDocumentoPagoResponse response = blTransacPagos.validaTipoDocumento(codDocto);

            // Determinar el código de respuesta HTTP basado en el código de la respuesta
            HttpStatus httpStatus;
            switch (response.getCodigo()) {
                case "200":
                    httpStatus = HttpStatus.OK;
                    break;
                case "204":
                    httpStatus = HttpStatus.NO_CONTENT;
                    break;
                case "400":
                    httpStatus = HttpStatus.BAD_REQUEST;
                    break;
                default:
                    httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
                    break;
            }

            log.info("Validación de tipo de documento completada con código: {}", response.getCodigo());
            return new ResponseEntity<>(response, httpStatus);

        } catch (Exception e) {
            log.error("Error al validar tipo de documento para codDocto={}: {}", codDocto, e.getMessage(), e);

            // Crear respuesta de error
            TipoDocumentoPagoResponse errorResponse = new TipoDocumentoPagoResponse();
            errorResponse.setCodigo("500");
            errorResponse.setMensaje("Error interno del servidor: " + e.getMessage());

            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
