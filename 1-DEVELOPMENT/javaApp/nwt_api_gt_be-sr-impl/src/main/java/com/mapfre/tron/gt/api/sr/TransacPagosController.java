package com.mapfre.tron.gt.api.sr;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import com.mapfre.tron.gt.api.bl.IBlTransacPagos;
import com.mapfre.tron.gt.api.model.ValidaEstadoDeducibleResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleFacResponse;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 * Controlador REST para operaciones de transacciones de pagos.
 */
@RestController
@Slf4j
@Api(tags = {"TransacPagos"})
public class TransacPagosController implements TransacPagosApi {

    @Autowired
    private IBlTransacPagos blTransacPagos;

    @Override
    public ResponseEntity<ValidaEstadoDeducibleResponse> validaEstadoDeducible(Integer codCia, String numSini) {
        log.info("Recibida solicitud para validar estado de deducible con codCia={}, numSini={}", codCia, numSini);

        try {
            // Llamar al servicio de negocio
            ValidaEstadoDeducibleResponse response = blTransacPagos.validaEstadoDeducible(codCia, numSini);

            // Verificar si se obtuvo una respuesta válida
            if (response == null || response.getResp() == null || response.getResp().isEmpty()) {
                log.warn("No se obtuvo respuesta válida para la validación del deducible");
                return new ResponseEntity<>(response, HttpStatus.NO_CONTENT);
            }

            log.info("Validación de estado de deducible completada exitosamente para numSini={}", numSini);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al validar estado de deducible para numSini={}: {}", numSini, e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<MontoDeducibleFacResponse> getMontoDeducibleFac(Integer codCia, String numSini, String mcaFactura, Integer numCuota) {
        log.info("Recibida solicitud para obtener monto deducible para facturación con codCia={}, numSini={}, mcaFactura={}, numCuota={}",
                 codCia, numSini, mcaFactura, numCuota);

        try {
            // Llamar al servicio de negocio
            MontoDeducibleFacResponse response = blTransacPagos.getMontoDeducibleFac(codCia, numSini, mcaFactura, numCuota);

            // Verificar si se obtuvo una respuesta válida
            if (response == null) {
                log.warn("No se obtuvo respuesta válida para el cálculo del monto deducible");
                return new ResponseEntity<>(response, HttpStatus.NO_CONTENT);
            }

            log.info("Cálculo de monto deducible para facturación completado exitosamente para numSini={}", numSini);
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Error al obtener monto deducible para facturación para numSini={}: {}", numSini, e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
