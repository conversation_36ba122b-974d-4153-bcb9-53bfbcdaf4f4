package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycUsuario;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycUsuario.
 */
@Slf4j
public class PycUsuarioRowMapper implements RowMapper<PycUsuario> {

    @Override
    public PycUsuario mapRow(ResultSet rs, int rowNum) throws SQLException {

        PycUsuario usuario = new PycUsuario();

            usuario.setIdUsuario(rs.getInt("ID_USUARIO"));
            usuario.setNombre(rs.getString("NOMBRE"));
            usuario.setFechaCreacion(rs.getString("FECHA_CREACION"));
            usuario.setEstado(rs.getString("ESTADO"));
            usuario.setFechaBaja(rs.getString("FECHA_BAJA"));
            usuario.setUsuarioBaseDatos(rs.getString("USUARIO_BASE_DATOS"));
            usuario.setEmail(rs.getString("EMAIL"));
            usuario.setGenero(rs.getString("GENERO"));
        
        return usuario;
    }
}
