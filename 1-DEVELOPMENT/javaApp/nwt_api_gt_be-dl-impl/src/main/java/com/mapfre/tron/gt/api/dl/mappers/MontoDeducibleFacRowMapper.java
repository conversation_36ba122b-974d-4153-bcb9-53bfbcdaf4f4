package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.MontoDeducibleFacResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos MontoDeducibleFacResponse.
 */
@Slf4j
public class MontoDeducibleFacRowMapper implements RowMapper<MontoDeducibleFacResponse> {

    @Override
    public MontoDeducibleFacResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto MontoDeducibleFacResponse", rowNum);
        
        MontoDeducibleFacResponse response = new MontoDeducibleFacResponse();
        
        try {
            response.setMoneda(rs.getString("moneda"));
            response.setMtoDeducible(rs.getDouble("mtoDeducible"));
            response.setMtoIva(rs.getDouble("mtoIva"));
            response.setMtoCuotas(rs.getDouble("mtoCuotas"));
            response.setMtoTotal(rs.getDouble("mtoTotal"));
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return response;
    }
}
