package com.mapfre.tron.gt.api.dl.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.mapfre.tron.gt.api.model.PycPeticionPerfil;
import static com.mapfre.tron.gt.api.sr.CacheApi.log;

import lombok.extern.slf4j.Slf4j;

/**
 * Mapper para convertir resultados de la base de datos a objetos PycPeticionPerfil.
 */
@Slf4j
public class PycPeticionPerfilRowMapper implements RowMapper<PycPeticionPerfil> {

    @Override
    public PycPeticionPerfil mapRow(ResultSet rs, int rowNum) throws SQLException {
        log.debug("Mapeando fila {} a objeto PycPeticionPerfil", rowNum);

        PycPeticionPerfil peticionPerfil = new PycPeticionPerfil();

        try {
            // Campos principales de la petición
            peticionPerfil.setIdPeticion(rs.getInt("ID_PETICION"));
            peticionPerfil.setNombrePeticion(rs.getString("NOMBRE_PETICION"));
            peticionPerfil.setDescripcionPeticion(rs.getString("DESCRIPCION_PETICION"));
            peticionPerfil.setFechaCreacion(rs.getString("FECHA_CREACION"));
            
            // Información del proceso
            peticionPerfil.setIdProceso(rs.getInt("ID_PROCESO"));
            peticionPerfil.setNombreProceso(rs.getString("TIPO_PETICION")); // Usando TIPO_PETICION como nombre del proceso
            
            // Información del perfil
            peticionPerfil.setIdPerfil(rs.getInt("ID_PERFIL"));
            peticionPerfil.setNombrePerfil(rs.getString("NOMBRE_PERFIL"));
            
            // Información del estado
            peticionPerfil.setIdEstado(rs.getString("ID_ESTADO"));
            peticionPerfil.setNombreEstado(rs.getString("NOMBRE_ESTADO"));
            
            // Información del usuario solicitante
            peticionPerfil.setIdUsuarioSolicitante(rs.getInt("ID_USUARIO_SOLICITANTE"));
            peticionPerfil.setNombreUsuarioSolicitante(rs.getString("USUARIO_SOLI"));
            
            // Información del cliente
            peticionPerfil.setNombreCliente(rs.getString("NOMBRE_CLIENTE"));
            peticionPerfil.setTelefonoCliente(rs.getString("TELEFONO_CLIENTE"));
            peticionPerfil.setCorreoCliente(rs.getString("CORREO_CLIENTE"));
            
            // Prioridad
            peticionPerfil.setPrioridad(rs.getString("PRIORI_DESC"));
            
            // Campos opcionales que pueden no estar presentes en todos los casos
            try {
                // Información del canal (puede no estar presente)
                if (rs.findColumn("ID_CANAL") > 0) {
                    peticionPerfil.setIdCanal(rs.getInt("ID_CANAL"));
                }
            } catch (SQLException e) {
                // Campo no presente, continuar
                log.debug("Campo ID_CANAL no presente en el resultado");
            }
            
            try {
                // Información de la oficina (puede no estar presente)
                if (rs.findColumn("ID_OFICINA") > 0) {
                    peticionPerfil.setIdOficina(rs.getInt("ID_OFICINA"));
                }
            } catch (SQLException e) {
                // Campo no presente, continuar
                log.debug("Campo ID_OFICINA no presente en el resultado");
            }
            
            // Campos adicionales que pueden estar presentes según el query dinámico
            try {
                peticionPerfil.setNombreCanal(rs.getString("ORIGEN_DESCRIPCION"));
            } catch (SQLException e) {
                log.debug("Campo ORIGEN_DESCRIPCION no presente en el resultado");
            }
            
            try {
                peticionPerfil.setNombreOficina(rs.getString("CODCIA_DESCRIPCION"));
            } catch (SQLException e) {
                log.debug("Campo CODCIA_DESCRIPCION no presente en el resultado");
            }
            
        } catch (SQLException e) {
            log.error("Error al mapear columna: {}", e.getMessage(), e);
            throw e;
        }
        
        return peticionPerfil;
    }
}
