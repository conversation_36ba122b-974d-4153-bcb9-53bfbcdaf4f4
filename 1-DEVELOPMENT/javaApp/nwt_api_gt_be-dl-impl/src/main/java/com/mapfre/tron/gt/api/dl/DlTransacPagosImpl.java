package com.mapfre.tron.gt.api.dl;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.mapfre.tron.gt.api.commons.DatabaseConstants;
import com.mapfre.tron.gt.api.model.ValidaEstadoDeducibleResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleFacResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleResponse;
import com.mapfre.tron.gt.api.model.InfoDeducibleResponse;
import com.mapfre.tron.gt.api.model.AvisoPagoPolizaGrupoResponse;
import com.mapfre.tron.gt.api.model.TipoDocumentoPago;
import com.mapfre.tron.gt.api.dl.mappers.TipoDocumentoPagoRowMapper;
import java.util.ArrayList;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

import static com.mapfre.tron.gt.api.sr.CacheApi.log;

@Repository
@Slf4j
public class DlTransacPagosImpl implements IDlTransacPagos {

    @Autowired
    private DataSource dataSource;

    @Override
    public ValidaEstadoDeducibleResponse validaEstadoDeducible(Integer codCia, String numSini) {
        log.info("Ejecutando función validaEstadoDeducible con codCia={}, numSini={}", codCia, numSini);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.CO_K_TRANSAC_LINEA_MGT + "." +
                          DatabaseConstants.F_DEV_VALIDACION +
                          "(p_cod_cia => ?, p_num_sini => ?) resp FROM dual";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros de entrada
            stmt.setInt(1, codCia);
            stmt.setString(2, numSini);

            log.debug("Ejecutando consulta SQL: {}", sqlQuery);
            log.debug("Parámetros: codCia={}, numSini={}", codCia, numSini);

            // Ejecutar la consulta
            try (ResultSet rs = stmt.executeQuery()) {
                ValidaEstadoDeducibleResponse response = new ValidaEstadoDeducibleResponse();

                if (rs.next()) {
                    String respuesta = rs.getString("resp");
                    response.setResp(respuesta);
                    log.info("Respuesta obtenida: {}", respuesta);
                } else {
                    log.warn("No se obtuvo respuesta de la función validaEstadoDeducible");
                    response.setResp("No se pudo validar el estado del deducible");
                }

                return response;
            }

        } catch (SQLException e) {
            log.error("Error al ejecutar la función validaEstadoDeducible: {}", e.getMessage(), e);
            throw new RuntimeException("Error al validar el estado del deducible", e);
        }
    }

    @Override
    public MontoDeducibleFacResponse getMontoDeducibleFac(Integer codCia, String numSini, String mcaFactura, Integer numCuota) {
        log.info("Ejecutando función getMontoDeducibleFac con codCia={}, numSini={}, mcaFactura={}, numCuota={}",
                 codCia, numSini, mcaFactura, numCuota);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.CO_K_TRANSAC_LINEA_MGT + "." +
                          DatabaseConstants.F_DEV_CALCULO_DEDU +
                          "(p_cod_cia => ?, p_num_sini => ?, p_mca_factura => ?, p_num_cuota => ?) f_dev_calculo_dedu FROM dual";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros de entrada
            stmt.setInt(1, codCia);
            stmt.setString(2, numSini);
            stmt.setString(3, mcaFactura);
            stmt.setInt(4, numCuota);

            log.debug("Ejecutando consulta SQL: {}", sqlQuery);
            log.debug("Parámetros: codCia={}, numSini={}, mcaFactura={}, numCuota={}", codCia, numSini, mcaFactura, numCuota);

            // Ejecutar la consulta
            try (ResultSet rs = stmt.executeQuery()) {
                MontoDeducibleFacResponse response = new MontoDeducibleFacResponse();

                if (rs.next()) {
                    String resultado = rs.getString("f_dev_calculo_dedu");
                    log.info("Resultado obtenido: {}", resultado);

                    // Parsear la respuesta que viene en formato: {<MONEDA=HNL,MTO_DEDUCIBLE=null,MTO_IVA=null,MTO_CUOTAS=0,MTO_TOTAL=null>,}
                    if (resultado != null && !resultado.isEmpty()) {
                        response = parseMontoDeducibleResponse(resultado);
                    } else {
                        log.warn("Respuesta vacía de la función getMontoDeducibleFac");
                        // Establecer valores por defecto
                        response.setMoneda("HNL");
                        response.setMtoCuotas(0.0);
                    }
                } else {
                    log.warn("No se obtuvo respuesta de la función getMontoDeducibleFac");
                    // Establecer valores por defecto
                    response.setMoneda("HNL");
                    response.setMtoCuotas(0.0);
                }

                return response;
            }

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getMontoDeducibleFac: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener el monto del deducible para facturación", e);
        }
    }

    /**
     * Parsea la respuesta de la función Oracle que viene en formato:
     * {<MONEDA=HNL,MTO_DEDUCIBLE=null,MTO_IVA=null,MTO_CUOTAS=0,MTO_TOTAL=null>,}
     */
    private MontoDeducibleFacResponse parseMontoDeducibleResponse(String resultado) {
        MontoDeducibleFacResponse response = new MontoDeducibleFacResponse();

        try {
            // Remover caracteres externos: {< y >,}
            String content = resultado.replaceAll("[{}<>,]", "").trim();

            // Dividir por comas para obtener cada campo
            String[] campos = content.split(",");

            for (String campo : campos) {
                String[] keyValue = campo.split("=", 2);
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim();
                    String value = keyValue[1].trim();

                    switch (key) {
                        case "MONEDA":
                            response.setMoneda("null".equals(value) ? null : value);
                            break;
                        case "MTO_DEDUCIBLE":
                            response.setMtoDeducible("null".equals(value) ? null : Double.parseDouble(value));
                            break;
                        case "MTO_IVA":
                            response.setMtoIva("null".equals(value) ? null : Double.parseDouble(value));
                            break;
                        case "MTO_CUOTAS":
                            response.setMtoCuotas("null".equals(value) ? null : Double.parseDouble(value));
                            break;
                        case "MTO_TOTAL":
                            response.setMtoTotal("null".equals(value) ? null : Double.parseDouble(value));
                            break;
                        default:
                            log.debug("Campo desconocido en respuesta: {}", key);
                            break;
                    }
                }
            }

        } catch (Exception e) {
            log.error("Error al parsear la respuesta: {}", resultado, e);
            // En caso de error, establecer valores por defecto
            response.setMoneda("HNL");
            response.setMtoCuotas(0.0);
        }

        return response;
    }

    @Override
    public MontoDeducibleResponse getMontoDeducible(Integer codCia, String numSini) {
        log.info("Ejecutando función getMontoDeducible con codCia={}, numSini={}", codCia, numSini);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.CO_K_TRANSAC_LINEA_MGT + "." +
                          DatabaseConstants.FP_OBTIENE_DEDUCIBLE +
                          "(p_cod_cia => ?, p_num_sini => ?) fp_obtiene_deducible FROM dual";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros de entrada
            stmt.setInt(1, codCia);
            stmt.setString(2, numSini);

            log.debug("Ejecutando consulta SQL: {}", sqlQuery);
            log.debug("Parámetros: codCia={}, numSini={}", codCia, numSini);

            // Ejecutar la consulta
            try (ResultSet rs = stmt.executeQuery()) {
                MontoDeducibleResponse response = new MontoDeducibleResponse();

                if (rs.next()) {
                    Double monto = rs.getDouble("fp_obtiene_deducible");
                    // Verificar si el valor es null en la base de datos
                    if (rs.wasNull()) {
                        monto = null;
                    }
                    response.setMonto(monto);
                    log.info("Monto del deducible obtenido: {}", monto);
                } else {
                    log.warn("No se obtuvo respuesta de la función getMontoDeducible");
                    response.setMonto(null);
                }

                return response;
            }

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getMontoDeducible: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener el monto del deducible", e);
        }
    }

    @Override
    public InfoDeducibleResponse getInfoDeducible(Integer codCia, String numLiq) {
        log.info("Ejecutando función getInfoDeducible con codCia={}, numLiq={}", codCia, numLiq);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.CO_K_TRANSAC_LINEA_MGT + "." +
                          DatabaseConstants.F_INFO_LIQUIDACION +
                          "(p_cod_cia => ?, p_num_liq => ?) f_info_liquidacion FROM dual";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros de entrada
            stmt.setInt(1, codCia);
            stmt.setString(2, numLiq);

            log.debug("Ejecutando consulta SQL: {}", sqlQuery);
            log.debug("Parámetros: codCia={}, numLiq={}", codCia, numLiq);

            // Ejecutar la consulta
            try (ResultSet rs = stmt.executeQuery()) {
                InfoDeducibleResponse response = new InfoDeducibleResponse();

                if (rs.next()) {
                    String resultado = rs.getString("f_info_liquidacion");
                    log.info("Resultado obtenido: {}", resultado);

                    // Parsear la respuesta que viene en formato complejo
                    if (resultado != null && !resultado.isEmpty()) {
                        response = parseInfoDeducibleResponse(resultado);
                    } else {
                        log.warn("Respuesta vacía de la función getInfoDeducible");
                    }
                } else {
                    log.warn("No se obtuvo respuesta de la función getInfoDeducible");
                }

                return response;
            }

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getInfoDeducible: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener la información del deducible", e);
        }
    }

    /**
     * Parsea la respuesta de la función Oracle que viene en formato complejo:
     * {<NUM_LIQ=110123003434,NUM_SINI=110130023003755,NUM_EXP=3,...>,}
     */
    private InfoDeducibleResponse parseInfoDeducibleResponse(String resultado) {
        InfoDeducibleResponse response = new InfoDeducibleResponse();

        try {
            // Remover caracteres externos: {< y >,}
            String content = resultado.replaceAll("[{}<>,]", "").trim();

            // Dividir por comas para obtener cada campo
            String[] campos = content.split(",");

            for (String campo : campos) {
                String[] keyValue = campo.split("=", 2);
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim();
                    String value = keyValue[1].trim();

                    // Convertir "null" string a null real
                    if ("null".equals(value)) {
                        value = null;
                    }

                    switch (key) {
                        case "NUM_LIQ":
                            response.setNumLiq(value);
                            break;
                        case "NUM_SINI":
                            response.setNumSini(value);
                            break;
                        case "NUM_EXP":
                            response.setNumExp(value != null ? Integer.parseInt(value) : null);
                            break;
                        case "NUM_POLIZA":
                            response.setNumPoliza(value);
                            break;
                        case "COD_NIVEL3":
                            response.setCodNivel3(value);
                            break;
                        case "NOM_NIVEL3":
                            response.setNomNivel3(value);
                            break;
                        case "COD_RAMO":
                            response.setCodRamo(value != null ? Integer.parseInt(value) : null);
                            break;
                        case "COD_SECTOR":
                            response.setCodSector(value != null ? Integer.parseInt(value) : null);
                            break;
                        case "NOM_SECTOR":
                            response.setNomSector(value);
                            break;
                        case "COD_TERCERO":
                            response.setCodTercero(value);
                            break;
                        case "NOM_TERCERO":
                            response.setNomTercero(value);
                            break;
                        case "COD_ACT_TERCERO":
                            response.setCodActTercero(value != null ? Integer.parseInt(value) : null);
                            break;
                        case "NOM_ACT_TERCERO":
                            response.setNomActTercero(value);
                            break;
                        case "TIP_DOCUM":
                            response.setTipDocum(value);
                            break;
                        case "COD_DOCUM":
                            response.setCodDocum(value);
                            break;
                        case "OBS":
                            response.setObs(value);
                            break;
                        case "FEC_LIQ":
                            response.setFecLiq(value);
                            break;
                        case "FEC_EST_PAGO":
                            response.setFecEstPago(value);
                            break;
                        case "FEC_PAGO":
                            response.setFecPago(value);
                            break;
                        case "COD_MON_LIQ":
                            response.setCodMonLiq(value != null ? Integer.parseInt(value) : null);
                            break;
                        case "COD_MON_LIQ_ISO":
                            response.setCodMonLiqIso(value);
                            break;
                        case "NUM_DECIMALES":
                            response.setNumDecimales(value != null ? Integer.parseInt(value) : null);
                            break;
                        case "COD_MON_PAGO":
                            response.setCodMonPago(value != null ? Integer.parseInt(value) : null);
                            break;
                        case "COD_MON_PAGO_ISO":
                            response.setCodMonPagoIso(value);
                            break;
                        case "IMP_LIQ_NETO":
                            response.setImpLiqNeto(value != null ? Double.parseDouble(value) : null);
                            break;
                        case "IMP_IVA":
                            response.setImpIva(value != null ? Double.parseDouble(value) : null);
                            break;
                        case "IMP_LIQ":
                            response.setImpLiq(value != null ? Double.parseDouble(value) : null);
                            break;
                        case "VAL_CAMBIO":
                            response.setValCambio(value != null ? Double.parseDouble(value) : null);
                            break;
                        case "TIP_DOCTO":
                            response.setTipDocto(value);
                            break;
                        default:
                            log.debug("Campo desconocido en respuesta: {}", key);
                            break;
                    }
                }
            }

        } catch (Exception e) {
            log.error("Error al parsear la respuesta: {}", resultado, e);
            // En caso de error, devolver respuesta vacía
            response = new InfoDeducibleResponse();
        }

        return response;
    }

    @Override
    public AvisoPagoPolizaGrupoResponse getAvisoPagoPolizaGrupo(Integer codCia, Integer numContrato, String numPolizaGrupo) {
        log.info("Ejecutando función getAvisoPagoPolizaGrupo con codCia={}, numContrato={}, numPolizaGrupo={}",
                 codCia, numContrato, numPolizaGrupo);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.CO_K_TRANSAC_LINEA_MGT + "." +
                          DatabaseConstants.F_DEV_AVISO_POLIZA_GRUPO +
                          "(p_cod_cia => ?, p_num_contrato => ?, p_num_poliza_grupo => ?) avisos_pago FROM dual";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros de entrada
            stmt.setInt(1, codCia);
            stmt.setInt(2, numContrato);
            stmt.setString(3, numPolizaGrupo);

            log.debug("Ejecutando consulta SQL: {}", sqlQuery);
            log.debug("Parámetros: codCia={}, numContrato={}, numPolizaGrupo={}", codCia, numContrato, numPolizaGrupo);

            // Ejecutar la consulta
            try (ResultSet rs = stmt.executeQuery()) {
                AvisoPagoPolizaGrupoResponse response = new AvisoPagoPolizaGrupoResponse();

                if (rs.next()) {
                    String resultado = rs.getString("avisos_pago");
                    log.info("Resultado obtenido: {}", resultado);

                    // Parsear la respuesta que viene en formato complejo
                    if (resultado != null && !resultado.isEmpty()) {
                        response = parseAvisoPagoPolizaGrupoResponse(resultado);
                    } else {
                        log.warn("Respuesta vacía de la función getAvisoPagoPolizaGrupo");
                    }
                } else {
                    log.warn("No se obtuvo respuesta de la función getAvisoPagoPolizaGrupo");
                }

                return response;
            }

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getAvisoPagoPolizaGrupo: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener el aviso de pago de póliza grupo", e);
        }
    }

    /**
     * Parsea la respuesta de la función Oracle que viene en formato:
     * {<COD_ACT_TERCERO=1,COD_AGT=null,COD_CIA=5,...>,}
     */
    private AvisoPagoPolizaGrupoResponse parseAvisoPagoPolizaGrupoResponse(String resultado) {
        AvisoPagoPolizaGrupoResponse response = new AvisoPagoPolizaGrupoResponse();

        try {
            // Remover caracteres externos: {< y >,}
            String content = resultado.replaceAll("[{}<>,]", "").trim();

            // Dividir por comas para obtener cada campo
            String[] campos = content.split(",");

            for (String campo : campos) {
                String[] keyValue = campo.split("=", 2);
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim();
                    String value = keyValue[1].trim();

                    // Convertir "null" string a null real
                    if ("null".equals(value)) {
                        value = null;
                    }

                    switch (key) {
                        case "COD_ACT_TERCERO":
                            response.setCodActTercero(value != null ? Integer.parseInt(value) : null);
                            break;
                        case "COD_AGT":
                            response.setCodAgt(value);
                            break;
                        case "COD_CIA":
                            response.setCodCia(value != null ? Integer.parseInt(value) : null);
                            break;
                        case "COD_DOCUM":
                            response.setCodDocum(value);
                            break;
                        case "COD_DOCUM_PAGO":
                            response.setCodDocumPago(value);
                            break;
                        case "COD_GESTOR":
                            response.setCodGestor(value != null ? Integer.parseInt(value) : null);
                            break;
                        case "COD_MON":
                            response.setCodMon(value != null ? Integer.parseInt(value) : null);
                            break;
                        case "FEC_MVTO":
                            response.setFecMvto(value);
                            break;
                        case "FEC_VCTO":
                            response.setFecVcto(value);
                            break;
                        case "IMP_DOCUM":
                            response.setImpDocum(value != null ? Double.parseDouble(value) : null);
                            break;
                        case "NUM_CONTRATO":
                            response.setNumContrato(value != null ? Integer.parseInt(value) : null);
                            break;
                        case "NUM_MVTO":
                            response.setNumMvto(value != null ? Integer.parseInt(value) : null);
                            break;
                        case "NUM_POLIZA":
                            response.setNumPoliza(value);
                            break;
                        case "NUM_POLIZA_CLIENTE":
                            response.setNumPolizaCliente(value);
                            break;
                        case "NUM_POLIZA_GRUPO":
                            response.setNumPolizaGrupo(value);
                            break;
                        case "TIP_DOCUM":
                            response.setTipDocum(value);
                            break;
                        case "TIP_DOCUM_PAGO":
                            response.setTipDocumPago(value);
                            break;
                        case "TIP_ESTADO":
                            response.setTipEstado(value);
                            break;
                        case "TIP_GESTOR":
                            response.setTipGestor(value);
                            break;
                        case "VAL_CAMBIO":
                            response.setValCambio(value != null ? Double.parseDouble(value) : null);
                            break;
                        default:
                            log.debug("Campo desconocido en respuesta: {}", key);
                            break;
                    }
                }
            }

        } catch (Exception e) {
            log.error("Error al parsear la respuesta: {}", resultado, e);
            // En caso de error, devolver respuesta vacía
            response = new AvisoPagoPolizaGrupoResponse();
        }

        return response;
    }

    @Override
    public List<TipoDocumentoPago> validaTipoDocumentoPago(Integer codCia, String requerimiento) {
        log.info("Ejecutando función validaTipoDocumentoPago con codCia={}, requerimiento={}", codCia, requerimiento);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.CO_K_PASARELA_PAGO_MGT + "." +
                          DatabaseConstants.F_VALIDA_TIP_DOCTO_PAGO +
                          "(?, ?) AS documentos_pago FROM dual";

        List<TipoDocumentoPago> result = new ArrayList<>();
        TipoDocumentoPagoRowMapper mapper = new TipoDocumentoPagoRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            stmt.setInt(1, codCia);
            stmt.setString(2, requerimiento);

            log.debug("Ejecutando consulta SQL: {}", sqlQuery);
            log.debug("Parámetros: codCia={}, requerimiento={}", codCia, requerimiento);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("documentos_pago");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función validaTipoDocumentoPago ejecutada correctamente. Registros encontrados: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función validaTipoDocumentoPago: {}", e.getMessage(), e);
            throw new RuntimeException("Error al validar tipos de documentos de pago", e);
        }
    }
}
