package com.mapfre.tron.gt.api.dl;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.mapfre.tron.gt.api.commons.DatabaseConstants;
import com.mapfre.tron.gt.api.model.ValidaEstadoDeducibleResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleFacResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleResponse;
import com.mapfre.tron.gt.api.model.InfoDeducibleResponse;
import com.mapfre.tron.gt.api.model.AvisoPagoPolizaGrupoResponse;
import com.mapfre.tron.gt.api.model.TipoDocumentoPago;
import com.mapfre.tron.gt.api.dl.mappers.TipoDocumentoPagoRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.InfoDeducibleRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.AvisoPagoPolizaGrupoRowMapper;
import com.mapfre.tron.gt.api.dl.mappers.MontoDeducibleFacRowMapper;
import java.util.ArrayList;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

import static com.mapfre.tron.gt.api.sr.CacheApi.log;

@Repository
@Slf4j
public class DlTransacPagosImpl implements IDlTransacPagos {

    @Autowired
    private DataSource dataSource;

    @Override
    public ValidaEstadoDeducibleResponse validaEstadoDeducible(Integer codCia, String numSini) {
        log.info("Ejecutando función validaEstadoDeducible con codCia={}, numSini={}", codCia, numSini);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.CO_K_TRANSAC_LINEA_MGT + "." +
                          DatabaseConstants.F_DEV_VALIDACION +
                          "(p_cod_cia => ?, p_num_sini => ?) resp FROM dual";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros de entrada
            stmt.setInt(1, codCia);
            stmt.setString(2, numSini);

            log.debug("Ejecutando consulta SQL: {}", sqlQuery);
            log.debug("Parámetros: codCia={}, numSini={}", codCia, numSini);

            // Ejecutar la consulta
            try (ResultSet rs = stmt.executeQuery()) {
                ValidaEstadoDeducibleResponse response = new ValidaEstadoDeducibleResponse();

                if (rs.next()) {
                    String respuesta = rs.getString("resp");
                    response.setResp(respuesta);
                    log.info("Respuesta obtenida: {}", respuesta);
                } else {
                    log.warn("No se obtuvo respuesta de la función validaEstadoDeducible");
                    response.setResp("No se pudo validar el estado del deducible");
                }

                return response;
            }

        } catch (SQLException e) {
            log.error("Error al ejecutar la función validaEstadoDeducible: {}", e.getMessage(), e);
            throw new RuntimeException("Error al validar el estado del deducible", e);
        }
    }

    @Override
    public MontoDeducibleFacResponse getMontoDeducibleFac(Integer codCia, String numSini, String mcaFactura, Integer numCuota) {
        log.info("Ejecutando función getMontoDeducibleFac con codCia={}, numSini={}, mcaFactura={}, numCuota={}",
                 codCia, numSini, mcaFactura, numCuota);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.CO_K_TRANSAC_LINEA_MGT + "." +
                          DatabaseConstants.F_DEV_CALCULO_DEDU +
                          "(?, ?, ?, ?) AS f_dev_calculo_dedu FROM dual";

        MontoDeducibleFacRowMapper mapper = new MontoDeducibleFacRowMapper();
        MontoDeducibleFacResponse result = null;

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            stmt.setInt(1, codCia);
            stmt.setString(2, numSini);
            stmt.setString(3, mcaFactura);
            stmt.setInt(4, numCuota);

            log.debug("Ejecutando consulta SQL: {}", sqlQuery);
            log.debug("Parámetros: codCia={}, numSini={}, mcaFactura={}, numCuota={}", codCia, numSini, mcaFactura, numCuota);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("f_dev_calculo_dedu");
                        if (rs.next()) {
                            result = mapper.mapRow(rs, 0);
                        }
                    }
                }
            }

            if (result == null) {
                log.warn("No se obtuvo respuesta de la función getMontoDeducibleFac");
                result = new MontoDeducibleFacResponse();
                // Establecer valores por defecto
                result.setMoneda("HNL");
                result.setMtoCuotas(0.0);
            }

            log.info("Función getMontoDeducibleFac ejecutada correctamente");
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getMontoDeducibleFac: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener el monto del deducible para facturación", e);
        }
    }



    @Override
    public MontoDeducibleResponse getMontoDeducible(Integer codCia, String numSini) {
        log.info("Ejecutando función getMontoDeducible con codCia={}, numSini={}", codCia, numSini);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.CO_K_TRANSAC_LINEA_MGT + "." +
                          DatabaseConstants.FP_OBTIENE_DEDUCIBLE +
                          "(p_cod_cia => ?, p_num_sini => ?) fp_obtiene_deducible FROM dual";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros de entrada
            stmt.setInt(1, codCia);
            stmt.setString(2, numSini);

            log.debug("Ejecutando consulta SQL: {}", sqlQuery);
            log.debug("Parámetros: codCia={}, numSini={}", codCia, numSini);

            // Ejecutar la consulta
            try (ResultSet rs = stmt.executeQuery()) {
                MontoDeducibleResponse response = new MontoDeducibleResponse();

                if (rs.next()) {
                    Double monto = rs.getDouble("fp_obtiene_deducible");
                    // Verificar si el valor es null en la base de datos
                    if (rs.wasNull()) {
                        monto = null;
                    }
                    response.setMonto(monto);
                    log.info("Monto del deducible obtenido: {}", monto);
                } else {
                    log.warn("No se obtuvo respuesta de la función getMontoDeducible");
                    response.setMonto(null);
                }

                return response;
            }

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getMontoDeducible: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener el monto del deducible", e);
        }
    }

    @Override
    public InfoDeducibleResponse getInfoDeducible(Integer codCia, String numLiq) {
        log.info("Ejecutando función getInfoDeducible con codCia={}, numLiq={}", codCia, numLiq);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.CO_K_TRANSAC_LINEA_MGT + "." +
                          DatabaseConstants.F_INFO_LIQUIDACION +
                          "(?, ?) AS info_deducible FROM dual";

        InfoDeducibleRowMapper mapper = new InfoDeducibleRowMapper();
        InfoDeducibleResponse result = null;

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            stmt.setInt(1, codCia);
            stmt.setString(2, numLiq);

            log.debug("Ejecutando consulta SQL: {}", sqlQuery);
            log.debug("Parámetros: codCia={}, numLiq={}", codCia, numLiq);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("info_deducible");
                        if (rs.next()) {
                            result = mapper.mapRow(rs, 0);
                        }
                    }
                }
            }

            if (result == null) {
                log.warn("No se obtuvo respuesta de la función getInfoDeducible");
                result = new InfoDeducibleResponse();
            }

            log.info("Función getInfoDeducible ejecutada correctamente");
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getInfoDeducible: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener la información del deducible", e);
        }
    }



    @Override
    public List<AvisoPagoPolizaGrupoResponse> getAvisoPagoPolizaGrupo(Integer codCia, Integer numContrato, String numPolizaGrupo) {
        log.info("Ejecutando función getAvisoPagoPolizaGrupo con codCia={}, numContrato={}, numPolizaGrupo={}",
                 codCia, numContrato, numPolizaGrupo);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.CO_K_TRANSAC_LINEA_MGT + "." +
                          DatabaseConstants.F_DEV_AVISO_POLIZA_GRUPO +
                          "(?, ?, ?) AS avisos_pago FROM dual";

        List<AvisoPagoPolizaGrupoResponse> result = new ArrayList<>();
        AvisoPagoPolizaGrupoRowMapper mapper = new AvisoPagoPolizaGrupoRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            stmt.setInt(1, codCia);
            stmt.setInt(2, numContrato);
            stmt.setString(3, numPolizaGrupo);

            log.debug("Ejecutando consulta SQL: {}", sqlQuery);
            log.debug("Parámetros: codCia={}, numContrato={}, numPolizaGrupo={}", codCia, numContrato, numPolizaGrupo);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("avisos_pago");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función getAvisoPagoPolizaGrupo ejecutada correctamente. Registros encontrados: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getAvisoPagoPolizaGrupo: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener los avisos de pago de póliza grupo", e);
        }
    }



    @Override
    public List<TipoDocumentoPago> validaTipoDocumentoPago(Integer codCia, String requerimiento) {
        log.info("Ejecutando función validaTipoDocumentoPago con codCia={}, requerimiento={}", codCia, requerimiento);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.CO_K_PASARELA_PAGO_MGT + "." +
                          DatabaseConstants.F_VALIDA_TIP_DOCTO_PAGO +
                          "(?, ?) AS documentos_pago FROM dual";

        List<TipoDocumentoPago> result = new ArrayList<>();
        TipoDocumentoPagoRowMapper mapper = new TipoDocumentoPagoRowMapper();

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            stmt.setInt(1, codCia);
            stmt.setString(2, requerimiento);

            log.debug("Ejecutando consulta SQL: {}", sqlQuery);
            log.debug("Parámetros: codCia={}, requerimiento={}", codCia, requerimiento);

            if (stmt.execute()) {
                try (ResultSet rsMain = stmt.getResultSet()) {
                    if (rsMain.next()) {
                        ResultSet rs = (ResultSet) rsMain.getObject("documentos_pago");
                        int rowNum = 0;
                        while (rs.next()) {
                            result.add(mapper.mapRow(rs, rowNum++));
                        }
                    }
                }
            }

            log.info("Función validaTipoDocumentoPago ejecutada correctamente. Registros encontrados: {}", result.size());
            return result;

        } catch (SQLException e) {
            log.error("Error al ejecutar la función validaTipoDocumentoPago: {}", e.getMessage(), e);
            throw new RuntimeException("Error al validar tipos de documentos de pago", e);
        }
    }
}
