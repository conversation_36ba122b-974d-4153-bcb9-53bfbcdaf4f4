package com.mapfre.tron.gt.api.dl;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.mapfre.tron.gt.api.commons.DatabaseConstants;
import com.mapfre.tron.gt.api.model.ValidaEstadoDeducibleResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleFacResponse;
import com.mapfre.tron.gt.api.model.MontoDeducibleResponse;

import lombok.extern.slf4j.Slf4j;

import static com.mapfre.tron.gt.api.sr.CacheApi.log;

@Repository
@Slf4j
public class DlTransacPagosImpl implements IDlTransacPagos {

    @Autowired
    private DataSource dataSource;

    @Override
    public ValidaEstadoDeducibleResponse validaEstadoDeducible(Integer codCia, String numSini) {
        log.info("Ejecutando función validaEstadoDeducible con codCia={}, numSini={}", codCia, numSini);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.CO_K_TRANSAC_LINEA_MGT + "." +
                          DatabaseConstants.F_DEV_VALIDACION +
                          "(p_cod_cia => ?, p_num_sini => ?) resp FROM dual";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros de entrada
            stmt.setInt(1, codCia);
            stmt.setString(2, numSini);

            log.debug("Ejecutando consulta SQL: {}", sqlQuery);
            log.debug("Parámetros: codCia={}, numSini={}", codCia, numSini);

            // Ejecutar la consulta
            try (ResultSet rs = stmt.executeQuery()) {
                ValidaEstadoDeducibleResponse response = new ValidaEstadoDeducibleResponse();

                if (rs.next()) {
                    String respuesta = rs.getString("resp");
                    response.setResp(respuesta);
                    log.info("Respuesta obtenida: {}", respuesta);
                } else {
                    log.warn("No se obtuvo respuesta de la función validaEstadoDeducible");
                    response.setResp("No se pudo validar el estado del deducible");
                }

                return response;
            }

        } catch (SQLException e) {
            log.error("Error al ejecutar la función validaEstadoDeducible: {}", e.getMessage(), e);
            throw new RuntimeException("Error al validar el estado del deducible", e);
        }
    }

    @Override
    public MontoDeducibleFacResponse getMontoDeducibleFac(Integer codCia, String numSini, String mcaFactura, Integer numCuota) {
        log.info("Ejecutando función getMontoDeducibleFac con codCia={}, numSini={}, mcaFactura={}, numCuota={}",
                 codCia, numSini, mcaFactura, numCuota);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.CO_K_TRANSAC_LINEA_MGT + "." +
                          DatabaseConstants.F_DEV_CALCULO_DEDU +
                          "(p_cod_cia => ?, p_num_sini => ?, p_mca_factura => ?, p_num_cuota => ?) f_dev_calculo_dedu FROM dual";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros de entrada
            stmt.setInt(1, codCia);
            stmt.setString(2, numSini);
            stmt.setString(3, mcaFactura);
            stmt.setInt(4, numCuota);

            log.debug("Ejecutando consulta SQL: {}", sqlQuery);
            log.debug("Parámetros: codCia={}, numSini={}, mcaFactura={}, numCuota={}", codCia, numSini, mcaFactura, numCuota);

            // Ejecutar la consulta
            try (ResultSet rs = stmt.executeQuery()) {
                MontoDeducibleFacResponse response = new MontoDeducibleFacResponse();

                if (rs.next()) {
                    String resultado = rs.getString("f_dev_calculo_dedu");
                    log.info("Resultado obtenido: {}", resultado);

                    // Parsear la respuesta que viene en formato: {<MONEDA=HNL,MTO_DEDUCIBLE=null,MTO_IVA=null,MTO_CUOTAS=0,MTO_TOTAL=null>,}
                    if (resultado != null && !resultado.isEmpty()) {
                        response = parseMontoDeducibleResponse(resultado);
                    } else {
                        log.warn("Respuesta vacía de la función getMontoDeducibleFac");
                        // Establecer valores por defecto
                        response.setMoneda("HNL");
                        response.setMtoCuotas(0.0);
                    }
                } else {
                    log.warn("No se obtuvo respuesta de la función getMontoDeducibleFac");
                    // Establecer valores por defecto
                    response.setMoneda("HNL");
                    response.setMtoCuotas(0.0);
                }

                return response;
            }

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getMontoDeducibleFac: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener el monto del deducible para facturación", e);
        }
    }

    /**
     * Parsea la respuesta de la función Oracle que viene en formato:
     * {<MONEDA=HNL,MTO_DEDUCIBLE=null,MTO_IVA=null,MTO_CUOTAS=0,MTO_TOTAL=null>,}
     */
    private MontoDeducibleFacResponse parseMontoDeducibleResponse(String resultado) {
        MontoDeducibleFacResponse response = new MontoDeducibleFacResponse();

        try {
            // Remover caracteres externos: {< y >,}
            String content = resultado.replaceAll("[{}<>,]", "").trim();

            // Dividir por comas para obtener cada campo
            String[] campos = content.split(",");

            for (String campo : campos) {
                String[] keyValue = campo.split("=", 2);
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim();
                    String value = keyValue[1].trim();

                    switch (key) {
                        case "MONEDA":
                            response.setMoneda("null".equals(value) ? null : value);
                            break;
                        case "MTO_DEDUCIBLE":
                            response.setMtoDeducible("null".equals(value) ? null : Double.parseDouble(value));
                            break;
                        case "MTO_IVA":
                            response.setMtoIva("null".equals(value) ? null : Double.parseDouble(value));
                            break;
                        case "MTO_CUOTAS":
                            response.setMtoCuotas("null".equals(value) ? null : Double.parseDouble(value));
                            break;
                        case "MTO_TOTAL":
                            response.setMtoTotal("null".equals(value) ? null : Double.parseDouble(value));
                            break;
                        default:
                            log.debug("Campo desconocido en respuesta: {}", key);
                            break;
                    }
                }
            }

        } catch (Exception e) {
            log.error("Error al parsear la respuesta: {}", resultado, e);
            // En caso de error, establecer valores por defecto
            response.setMoneda("HNL");
            response.setMtoCuotas(0.0);
        }

        return response;
    }

    @Override
    public MontoDeducibleResponse getMontoDeducible(Integer codCia, String numSini) {
        log.info("Ejecutando función getMontoDeducible con codCia={}, numSini={}", codCia, numSini);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.CO_K_TRANSAC_LINEA_MGT + "." +
                          DatabaseConstants.FP_OBTIENE_DEDUCIBLE +
                          "(p_cod_cia => ?, p_num_sini => ?) fp_obtiene_deducible FROM dual";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros de entrada
            stmt.setInt(1, codCia);
            stmt.setString(2, numSini);

            log.debug("Ejecutando consulta SQL: {}", sqlQuery);
            log.debug("Parámetros: codCia={}, numSini={}", codCia, numSini);

            // Ejecutar la consulta
            try (ResultSet rs = stmt.executeQuery()) {
                MontoDeducibleResponse response = new MontoDeducibleResponse();

                if (rs.next()) {
                    Double monto = rs.getDouble("fp_obtiene_deducible");
                    // Verificar si el valor es null en la base de datos
                    if (rs.wasNull()) {
                        monto = null;
                    }
                    response.setFpObtieneDeducible(monto);
                    log.info("Monto del deducible obtenido: {}", monto);
                } else {
                    log.warn("No se obtuvo respuesta de la función getMontoDeducible");
                    response.setFpObtieneDeducible(null);
                }

                return response;
            }

        } catch (SQLException e) {
            log.error("Error al ejecutar la función getMontoDeducible: {}", e.getMessage(), e);
            throw new RuntimeException("Error al obtener el monto del deducible", e);
        }
    }
}
