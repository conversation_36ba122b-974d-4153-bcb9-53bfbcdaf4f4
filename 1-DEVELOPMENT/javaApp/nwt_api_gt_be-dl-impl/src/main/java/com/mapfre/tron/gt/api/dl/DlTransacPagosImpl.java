package com.mapfre.tron.gt.api.dl;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.mapfre.tron.gt.api.commons.DatabaseConstants;
import com.mapfre.tron.gt.api.model.ValidaEstadoDeducibleResponse;

import lombok.extern.slf4j.Slf4j;

import static com.mapfre.tron.gt.api.sr.CacheApi.log;

@Repository
@Slf4j
public class DlTransacPagosImpl implements IDlTransacPagos {

    @Autowired
    private DataSource dataSource;

    @Override
    public ValidaEstadoDeducibleResponse validaEstadoDeducible(Integer codCia, String numSini) {
        log.info("Ejecutando función validaEstadoDeducible con codCia={}, numSini={}", codCia, numSini);

        String sqlQuery = "SELECT " + DatabaseConstants.SCHEMA_NAME + "." +
                          DatabaseConstants.CO_K_TRANSAC_LINEA_MGT + "." +
                          DatabaseConstants.F_DEV_VALIDACION +
                          "(p_cod_cia => ?, p_num_sini => ?) resp FROM dual";

        try (Connection conn = dataSource.getConnection();
             CallableStatement stmt = conn.prepareCall(sqlQuery)) {

            // Establecer los parámetros de entrada
            stmt.setInt(1, codCia);
            stmt.setString(2, numSini);

            log.debug("Ejecutando consulta SQL: {}", sqlQuery);
            log.debug("Parámetros: codCia={}, numSini={}", codCia, numSini);

            // Ejecutar la consulta
            try (ResultSet rs = stmt.executeQuery()) {
                ValidaEstadoDeducibleResponse response = new ValidaEstadoDeducibleResponse();
                
                if (rs.next()) {
                    String respuesta = rs.getString("resp");
                    response.setResp(respuesta);
                    log.info("Respuesta obtenida: {}", respuesta);
                } else {
                    log.warn("No se obtuvo respuesta de la función validaEstadoDeducible");
                    response.setResp("No se pudo validar el estado del deducible");
                }

                return response;
            }

        } catch (SQLException e) {
            log.error("Error al ejecutar la función validaEstadoDeducible: {}", e.getMessage(), e);
            throw new RuntimeException("Error al validar el estado del deducible", e);
        }
    }
}
