swagger: '2.0'
info:
  description: Mapfre API EDGE Guatemala - API_EDGE
  version: '${project.version}'
  title: Mapfre API EDGE Guatemala  - API_EDGE
basePath: /newtron/api
host: api.mapfre.com
securityDefinitions:
  basicAuth:
    type: basic
security:
  - basicAuth: []
schemes:
  - http
  - https
tags:
  - name: "API_EDGE"
    description: "Servicios definidos para API_EDGE"
  - name: "API_EDGE"
    description: "Endpoints para la integración de Apps en Guatemala"
  - name: "Commons"
    description: "Servicios para endpoints comunes"
  - name: "PasarelaPago"
    description: "Servicios para operaciones de pasarela de pago"
  - name: "Tracking"
    description: "Servicios para seguimiento de siniestros"
  - name: "TransacPagos"
    description: "Servicios para transacciones de pagos"
  - name: "Pycges"
    description: "Servicios para gestión de peticiones a PYCGES"
paths:
  /cache/clear:
    post:
      tags:
        - Cache
      summary: Clear cache
      description: Clears the application's cache.
      operationId: clearCache
      responses:
        '200':
          description: Cache cleared successfully!
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /commons/tasa_cambio:
    post:
      tags:
        - Commons
      summary: Endpoint actualizacion tasa de cambio
      operationId: Actualizacion de tasa de cambio
      produces:
        - 'application/json'
      responses:
        '200':
          description: La tasa de cambio se ha actualizado correctamente
          schema:
            $ref: '#/definitions/Message'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /pasarela_pago/valida_tipo_documento:
    get:
      tags:
        - PasarelaPago
      summary: Validar tipos de documentos de pago(Recibos, Avisos)
      description: Obtiene los tipos de documentos de pago válidos según el requerimiento
      operationId: validaTipoDocumento
      produces:
        - 'application/json'
      parameters:
        - name: codDocto
          in: query
          description: Código de documento a filtrar
          required: true
          type: string
      responses:
        '200':
          description: Operación exitosa
          schema:
            $ref: '#/definitions/TipoDocumentoPagoResponse'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /tracking/get_siniestros:
    get:
      tags:
        - Tracking
      summary: Obtener listado de siniestros
      description: Obtiene la lista de siniestros según los criterios de búsqueda
      operationId: getSiniestros
      produces:
        - 'application/json'
      parameters:
        - name: codRamo
          in: query
          description: Código de ramo
          required: true
          type: integer
          format: int32
        - name: codInter
          in: query
          description: Código de intermediario
          required: false
          type: integer
          format: int32
        - name: numPoliza
          in: query
          description: Número de póliza
          required: false
          type: string
        - name: numSini
          in: query
          description: Número de siniestro
          required: false
          type: string
        - name: fechaInicio
          in: query
          description: Fecha de inicio para la búsqueda (formato yyyy-MM-dd)
          required: false
          type: string
          format: date
        - name: fechaFin
          in: query
          description: Fecha de fin para la búsqueda (formato yyyy-MM-dd)
          required: false
          type: string
          format: date
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/Siniestro'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /tracking/get_ramo_siniestro:
    get:
      tags:
        - Tracking
      summary: Obtener ramo de un siniestro
      description: Obtiene el código de ramo de un siniestro específico
      operationId: getRamoSiniestro
      produces:
        - 'application/json'
      parameters:
        - name: numSiniestro
          in: query
          description: Número de siniestro
          required: true
          type: string
      responses:
        '200':
          description: Operación exitosa
          schema:
            $ref: '#/definitions/RamoSiniestro'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /tracking/get_tracking_etapa_siniestro:
    get:
      tags:
        - Tracking
      summary: Obtener etapas de un siniestro
      description: Obtiene las etapas de un siniestro específico según su número y ramo
      operationId: getTrackingEtapaSiniestro
      produces:
        - 'application/json'
      parameters:
        - name: numSiniestro
          in: query
          description: Número de siniestro
          required: true
          type: string
        - name: codRamo
          in: query
          description: Código de ramo
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/EtapaSiniestro'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /tracking/get_siniestro_by_doc:
    get:
      tags:
        - Tracking
      summary: Obtener siniestro por documento
      description: Obtiene la información de un siniestro según su número, documento y tipo de documento
      operationId: getSiniestroByDoc
      produces:
        - 'application/json'
      parameters:
        - name: numSiniestro
          in: query
          description: Número de siniestro
          required: true
          type: string
        - name: numDoc
          in: query
          description: Número de documento del tomador
          required: true
          type: string
        - name: tipoDoc
          in: query
          description: Tipo de documento (NIT, DPI, etc.)
          required: true
          type: string
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/SiniestroNit'
  /tracking/get_url_siniestro:
    get:
      tags:
        - Tracking
      summary: Obtener URL encriptada de siniestro
      description: Obtiene una URL encriptada para acceder a un siniestro
      operationId: getUrlSiniestro
      produces:
        - 'application/json'
      parameters:
        - name: url
          in: query
          description: URL base
          required: true
          type: string
        - name: numSiniestro
          in: query
          description: Número de siniestro
          required: true
          type: string
        - name: tipoCliente
          in: query
          description: Tipo de cliente (T, A, etc.)
          required: true
          type: string
      responses:
        '200':
          description: Operación exitosa
          schema:
            $ref: '#/definitions/UrlSiniestro'
  /tracking/get_tracking_detalle_siniestro:
    get:
      tags:
        - Tracking
      summary: Obtener detalle de un siniestro
      description: Recupera el detalle de un siniestro con sus etapas, descripciones e imágenes
      operationId: getTrackingDetalleSiniestro
      produces:
        - 'application/json'
      parameters:
        - name: numSiniestro
          in: query
          description: Número de siniestro
          required: true
          type: string
        - name: codRamo
          in: query
          description: Código de ramo
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/DetalleSiniestro'
  /tracking/get_encuesta_siniestro:
    get:
      tags:
        - Tracking
      summary: Obtener encuesta de siniestro
      description: Recupera la información de encuesta de siniestro con sus preguntas y respuestas
      operationId: getEncuestaSiniestro
      produces:
        - 'application/json'
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/EncuestaSiniestro'
  /tracking/save_encuesta:
    post:
      tags:
        - Tracking
      summary: Guardar encuesta de siniestro
      description: Guarda las respuestas de una encuesta de siniestro
      operationId: saveEncuesta
      consumes:
        - 'application/json'
      produces:
        - 'application/json'
      parameters:
        - in: body
          name: body
          description: Datos de la encuesta a guardar
          required: true
          schema:
            $ref: '#/definitions/EncuestaSiniestroRequest'
      responses:
        '200':
          description: Operación exitosa
          schema:
            $ref: '#/definitions/EncuestaSiniestroResponse'
  /tracking/valida_ingreso_encuesta:
    get:
      tags:
        - Tracking
      summary: Validar ingreso de encuesta
      description: Valida si un siniestro puede tener una encuesta
      operationId: validaIngresoEncuesta
      produces:
        - 'application/json'
      parameters:
        - name: numSiniestro
          in: query
          description: Número de siniestro
          required: true
          type: string
      responses:
        '200':
          description: Operación exitosa
          schema:
            $ref: '#/definitions/ValidaIngresoEncuestaResponse'
  /tracking/get_siniestro_vehiculo:
    get:
      tags:
        - Tracking
      summary: Obtener información de siniestro de vehículo
      description: Obtiene la información de seguimiento de un siniestro de vehículo
      operationId: getSiniestroVehiculo
      produces:
        - 'application/json'
      parameters:
        - name: numSiniestro
          in: query
          description: Número de siniestro
          required: true
          type: string
        - name: codCia
          in: query
          description: Código de compañía
          required: true
          type: integer
          format: int32
        - name: numPoliza
          in: query
          description: Número de póliza
          required: true
          type: string
        - name: codFase
          in: query
          description: Código de fase
          required: true
          type: string
        - name: codRamo
          in: query
          description: Código de ramo
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/SiniestroVehiculo'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /transacPagos/valida_estado_deducible:
    get:
      tags:
        - TransacPagos
      summary: Validar estado del deducible
      description: Valida el estado del deducible para un siniestro específico
      operationId: validaEstadoDeducible
      produces:
        - 'application/json'
      parameters:
        - name: codCia
          in: query
          description: Código de compañía
          required: true
          type: integer
          format: int32
        - name: numSini
          in: query
          description: Número de siniestro
          required: true
          type: string
      responses:
        '200':
          description: Operación exitosa
          schema:
            $ref: '#/definitions/ValidaEstadoDeducibleResponse'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /transacPagos/get_mto_deducible_fac:
    get:
      tags:
        - TransacPagos
      summary: Obtener monto del deducible para facturación
      description: Obtiene el monto del deducible para facturación de un siniestro específico
      operationId: getMontoDeducibleFac
      produces:
        - 'application/json'
      parameters:
        - name: codCia
          in: query
          description: Código de compañía
          required: true
          type: integer
          format: int32
        - name: numSini
          in: query
          description: Número de siniestro
          required: true
          type: string
        - name: mcaFactura
          in: query
          description: Marca de factura ('S' o 'N')
          required: true
          type: string
        - name: numCuota
          in: query
          description: Número de cuota
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            $ref: '#/definitions/MontoDeducibleFacResponse'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /transacPagos/get_mto_deducible:
    get:
      tags:
        - TransacPagos
      summary: Obtener monto del deducible
      description: Obtiene el monto del deducible de un siniestro específico
      operationId: getMontoDeducible
      produces:
        - 'application/json'
      parameters:
        - name: codCia
          in: query
          description: Código de compañía
          required: true
          type: integer
          format: int32
        - name: numSini
          in: query
          description: Número de siniestro
          required: true
          type: string
      responses:
        '200':
          description: Operación exitosa
          schema:
            $ref: '#/definitions/MontoDeducibleResponse'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /transacPagos/get_info_deducible:
    get:
      tags:
        - TransacPagos
      summary: Obtener información del deducible
      description: Obtiene la información de liquidación del deducible
      operationId: getInfoDeducible
      produces:
        - 'application/json'
      parameters:
        - name: codCia
          in: query
          description: Código de compañía
          required: true
          type: integer
          format: int32
        - name: numLiq
          in: query
          description: Número de liquidación
          required: true
          type: string
      responses:
        '200':
          description: Operación exitosa
          schema:
            $ref: '#/definitions/InfoDeducibleResponse'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /pycges/usuarios:
    get:
      tags:
        - Pycges
      summary: Obtener lista de usuarios
      description: Obtiene la lista de usuarios de PYCGES
      operationId: getUsuarios
      produces:
        - 'application/json'
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycUsuario'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /pycges/get_aplicaciones:
    get:
      tags:
        - Pycges
      summary: Obtener aplicaciones por proceso
      description: Obtiene la lista de aplicaciones asociadas a un proceso
      operationId: getAplicaciones
      produces:
        - 'application/json'
      parameters:
        - name: idProceso
          in: query
          description: ID del proceso
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycAplicacion'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /pycges/get_perfiles:
    get:
      tags:
        - Pycges
      summary: Obtener lista de perfiles
      description: Obtiene la lista completa de perfiles activos
      operationId: getPerfiles
      produces:
        - 'application/json'
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycPerfil'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /pycges/get_usuario_perfil:
    get:
      tags:
        - Pycges
      summary: Obtener usuarios por perfil
      description: Obtiene la lista de usuarios asociados a un perfil específico
      operationId: getUsuariosPerfil
      produces:
        - 'application/json'
      parameters:
        - name: idPerfil
          in: query
          description: ID del perfil
          required: false
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycUsuarioPerfil'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /pycges/get_listado_asigna:
    get:
      tags:
        - Pycges
      summary: Obtener listado de asignaciones por perfil
      description: Obtiene la lista de usuarios asignados a un perfil específico
      operationId: getListadoAsigna
      produces:
        - 'application/json'
      parameters:
        - name: idPerfil
          in: query
          description: ID del perfil
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycListadoAsigna'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /pycges/get_proceso_por_tipo:
    get:
      tags:
        - Pycges
      summary: Obtener procesos por tipo de petición
      description: Obtiene la lista de procesos asociados a un tipo de petición específico
      operationId: getProcesosPorTipo
      produces:
        - 'application/json'
      parameters:
        - name: idTipo
          in: query
          description: ID del tipo de petición
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycProcesoPorTipo'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /pycges/get_query_dat_var:
    get:
      tags:
        - Pycges
      summary: Obtener query de dato variable
      description: Obtiene la consulta asociada a un dato variable específico
      operationId: getQueryDatVar
      produces:
        - 'application/json'
      parameters:
        - name: idProceso
          in: query
          description: ID del proceso
          required: true
          type: integer
          format: int32
        - name: idSeccion
          in: query
          description: ID de la sección
          required: true
          type: integer
          format: int32
        - name: idDatoVar
          in: query
          description: ID del dato variable
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            $ref: '#/definitions/PycGetQueryDatVar'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /pycges/get_src_multiple_option:
    get:
      tags:
        - Pycges
      summary: Verificar opción de selección múltiple
      description: Verifica si un perfil tiene la opción de selección múltiple
      operationId: getSrcMultipleOption
      produces:
        - 'application/json'
      parameters:
        - name: idPerfil
          in: query
          description: ID del perfil
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            $ref: '#/definitions/PycSrcMultipleOption'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /pycges/get_usuarios_asig:
    get:
      tags:
        - Pycges
      summary: Obtener usuarios asignados
      description: Obtiene la lista de usuarios asignados según proceso, perfil, aplicación y estado
      operationId: getUsuariosAsig
      produces:
        - 'application/json'
      parameters:
        - name: idProceso
          in: query
          description: ID del proceso
          required: true
          type: integer
          format: int32
        - name: idPerfil
          in: query
          description: ID del perfil
          required: true
          type: integer
          format: int32
        - name: idAplicacion
          in: query
          description: ID de la aplicación
          required: true
          type: integer
          format: int32
        - name: idEstado
          in: query
          description: ID del estado
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycGetUsuariosAsig'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
  /pycges/get_usu_perfil_proceso:
    get:
      tags:
        - Pycges
      summary: Obtener usuarios por perfil y proceso
      description: Obtiene la lista de usuarios asociados a un perfil y proceso específicos
      operationId: getUsuPerfilProceso
      produces:
        - 'application/json'
      parameters:
        - name: idPerfil
          in: query
          description: ID del perfil
          required: true
          type: integer
          format: int32
        - name: idProceso
          in: query
          description: ID del proceso
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycUsuPerfilProceso'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_usuario_procesos:
    get:
      tags:
        - Pycges
      summary: Obtener procesos por usuario
      description: Obtiene la lista de procesos asociados a un usuario específico
      operationId: getUsuarioProcesos
      produces:
        - 'application/json'
      parameters:
        - name: usuario
          in: query
          description: Nombre de usuario
          required: true
          type: string
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycUsuProcesos'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/update_perfil:
    post:
      tags:
        - Pycges
      summary: Actualizar o crear perfil de usuario
      description: Actualiza o crea un registro de perfil para un usuario específico
      operationId: updatePerfil
      consumes:
        - 'application/json'
      produces:
        - 'application/json'
      parameters:
        - name: perfilData
          in: body
          description: Datos del perfil a actualizar o crear
          required: true
          schema:
            $ref: '#/definitions/PycUpdPerfil'
      responses:
        '200':
          description: Operación exitosa
          schema:
            $ref: '#/definitions/PycUsuarioResponse'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/update_usuario:
    post:
      tags:
        - Pycges
      summary: Actualizar o crear usuario
      description: Actualiza o crea un registro de usuario con sus datos personales y departamento
      operationId: updateUsuario
      consumes:
        - 'application/json'
      produces:
        - 'application/json'
      parameters:
        - name: usuarioData
          in: body
          description: Datos del usuario a actualizar o crear
          required: true
          schema:
            $ref: '#/definitions/PycUpdUsuario'
      responses:
        '200':
          description: Operación exitosa
          schema:
            $ref: '#/definitions/PycUsuarioResponse'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/crm_comercial/crea_solicitud:
    post:
      tags:
        - Pycges
      summary: Crear solicitud
      description: Crea una nueva solicitud en el sistema PYCGES
      operationId: creaSolicitud
      consumes:
        - 'application/json'
      produces:
        - 'application/json'
      parameters:
        - name: solicitudData
          in: body
          description: Datos de la solicitud a crear
          required: true
          schema:
            $ref: '#/definitions/PycCreaSolicitud'
      responses:
        '200':
          description: Operación exitosa
          schema:
            $ref: '#/definitions/PycSolicitudResponse'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/insert_peticion:
    post:
      tags:
        - Pycges
      summary: Insertar petición
      description: Inserta una nueva petición en el sistema PYCGES
      operationId: insertPeticion
      consumes:
        - 'application/json'
      produces:
        - 'application/json'
      parameters:
        - name: peticionData
          in: body
          description: Datos de la petición a insertar
          required: true
          schema:
            $ref: '#/definitions/PycInsertPeticion'
      responses:
        '200':
          description: Operación exitosa
          schema:
            $ref: '#/definitions/PycPeticionResponse'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_peticion_perfil:
    get:
      tags:
        - Pycges
      summary: Obtener peticiones por perfil
      description: Obtiene la lista de peticiones filtradas por perfil, proceso, estado y otros criterios opcionales
      operationId: getPeticionPerfil
      produces:
        - 'application/json'
      parameters:
        - name: idPerfil
          in: query
          description: ID del perfil
          required: true
          type: integer
          format: int32
        - name: idProceso
          in: query
          description: ID del proceso
          required: true
          type: integer
          format: int32
        - name: idEstado
          in: query
          description: ID del estado
          required: true
          type: string
        - name: idPeticion
          in: query
          description: ID de la petición (opcional)
          required: false
          type: integer
          format: int32
        - name: idCanal
          in: query
          description: ID del canal (opcional)
          required: false
          type: integer
          format: int32
        - name: idUsuario
          in: query
          description: ID del usuario (opcional)
          required: false
          type: integer
          format: int32
        - name: indSubs
          in: query
          description: Indicador de substitución (opcional)
          required: false
          type: string
        - name: idOficina
          in: query
          description: ID de la oficina (opcional)
          required: false
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycPeticionPerfil'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_peticion_sin_programador:
    get:
      tags:
        - Pycges
      summary: Obtener peticiones sin programador
      description: Obtiene la lista de peticiones que no tienen programador asignado
      operationId: getPeticionSinProgramador
      produces:
        - 'application/json'
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycPetSinProgramador'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_con_area:
    get:
      tags:
        - Pycges
      summary: Obtener áreas con peticiones
      description: Obtiene la lista de áreas que tienen usuarios con peticiones asociadas
      operationId: getConArea
      produces:
        - 'application/json'
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycAreaPeti'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_con_departamento:
    get:
      tags:
        - Pycges
      summary: Obtener departamentos por área
      description: Obtiene la lista de departamentos que tienen usuarios con peticiones, filtrados por área
      operationId: getConDepartamento
      produces:
        - 'application/json'
      parameters:
        - name: idArea
          in: query
          description: ID del área (opcional)
          required: false
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycDepartamento'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_con_solicitante:
    get:
      tags:
        - Pycges
      summary: Obtener usuarios solicitantes por área y departamento
      description: Obtiene la lista de usuarios que han realizado peticiones, filtrados por área y/o departamento
      operationId: getConSolicitante
      produces:
        - 'application/json'
      parameters:
        - name: idArea
          in: query
          description: ID del área (opcional)
          required: false
          type: integer
          format: int32
        - name: idDepartamento
          in: query
          description: ID del departamento (opcional)
          required: false
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycAreaUsuario'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_con_analista:
    get:
      tags:
        - Pycges
      summary: Obtener analistas por solicitante y prioridad
      description: Obtiene la lista de analistas disponibles filtrados por solicitante y prioridad de petición
      operationId: getConAnalista
      produces:
        - 'application/json'
      parameters:
        - name: idSolicitante
          in: query
          description: ID del usuario solicitante
          required: true
          type: integer
          format: int32
        - name: prioridad
          in: query
          description: Prioridad de la petición (opcional)
          required: false
          type: string
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycAnalista'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_con_estado:
    get:
      tags:
        - Pycges
      summary: Obtener estados por solicitante, prioridad y analista
      description: Obtiene la lista de estados de peticiones filtrados por solicitante, prioridad y analista
      operationId: getConEstado
      produces:
        - 'application/json'
      parameters:
        - name: idSolicitante
          in: query
          description: ID del usuario solicitante
          required: true
          type: integer
          format: int32
        - name: prioridad
          in: query
          description: Prioridad de la petición (usar 'null' para peticiones sin prioridad)
          required: true
          type: string
        - name: idAnalista
          in: query
          description: ID del analista asignado (opcional)
          required: false
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycConEstado'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_con_prioridad:
    get:
      tags:
        - Pycges
      summary: Obtener prioridades por solicitante, área y departamento
      description: Obtiene la lista de prioridades de peticiones filtradas por solicitante, área y departamento
      operationId: getConPrioridad
      produces:
        - 'application/json'
      parameters:
        - name: idSolicitante
          in: query
          description: ID del usuario solicitante (opcional)
          required: false
          type: integer
          format: int32
        - name: idArea
          in: query
          description: ID del área (opcional)
          required: false
          type: integer
          format: int32
        - name: idDepartamento
          in: query
          description: ID del departamento (opcional)
          required: false
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycPrioridad'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_tab_anios:
    get:
      tags:
        - Pycges
      summary: Obtener años disponibles por proceso
      description: Obtiene la lista de años distintos de las peticiones filtrados por proceso
      operationId: getTabAnios
      produces:
        - 'application/json'
      parameters:
        - name: idProceso
          in: query
          description: ID del proceso
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycTabAnios'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_tab_area:
    get:
      tags:
        - Pycges
      summary: Obtener áreas disponibles por proceso y año
      description: Obtiene la lista de áreas distintas de las peticiones filtradas por proceso y año
      operationId: getTabArea
      produces:
        - 'application/json'
      parameters:
        - name: idProceso
          in: query
          description: ID del proceso
          required: true
          type: integer
          format: int32
        - name: anio
          in: query
          description: Año de filtro (formato YYYY) (opcional)
          required: false
          type: string
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycTabArea'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_tab_estado:
    get:
      tags:
        - Pycges
      summary: Obtener estados disponibles por proceso, año y área
      description: Obtiene la lista de estados distintos de las peticiones filtradas por proceso, año y área
      operationId: getTabEstado
      produces:
        - 'application/json'
      parameters:
        - name: idProceso
          in: query
          description: ID del proceso
          required: true
          type: integer
          format: int32
        - name: anio
          in: query
          description: Año de filtro (formato YYYY) (opcional)
          required: false
          type: string
        - name: idArea
          in: query
          description: ID del área (opcional)
          required: false
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycTabEstado'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_peticion_avance:
    get:
      tags:
        - Pycges
      summary: Obtener avance de peticiones
      description: Obtiene el avance detallado de peticiones con información de observaciones, usuarios y estados, filtrado por proceso, área, estado, compañía y año
      operationId: getPeticionAvance
      produces:
        - 'application/json'
      parameters:
        - name: idProceso
          in: query
          description: ID del proceso (opcional)
          required: false
          type: integer
          format: int32
        - name: idArea
          in: query
          description: ID del área (opcional)
          required: false
          type: integer
          format: int32
        - name: estado
          in: query
          description: IDs de estados separados por coma (opcional, ej. "1,2,3")
          required: false
          type: string
        - name: codcia
          in: query
          description: Código de compañía (opcional)
          required: false
          type: string
        - name: anio
          in: query
          description: Año de filtro (opcional)
          required: false
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycPetAvance'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_usuarios_perfil_peticion:
    get:
      tags:
        - Pycges
      summary: Obtener usuarios por perfil y petición
      description: Obtiene la lista de usuarios asociados a un perfil específico para una petición y aplicación determinada
      operationId: getUsuariosPerfilPeticion
      produces:
        - 'application/json'
      parameters:
        - name: idPeticion
          in: query
          description: ID de la petición
          required: true
          type: integer
          format: int32
        - name: idPerfil
          in: query
          description: ID del perfil
          required: true
          type: integer
          format: int32
        - name: idAplicacion
          in: query
          description: ID de la aplicación
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycUsuPerfilPeti'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_peticion_filtros:
    get:
      tags:
        - Pycges
      summary: Obtener peticiones con filtros
      description: Obtiene la lista de peticiones filtradas por múltiples criterios como área, departamento, perfil, estado, usuario, tipo, fechas y proceso
      operationId: getPeticionFiltros
      produces:
        - 'application/json'
      parameters:
        - name: idArea
          in: query
          description: ID del área (opcional)
          required: false
          type: integer
          format: int32
        - name: idDepartamento
          in: query
          description: ID del departamento (opcional)
          required: false
          type: integer
          format: int32
        - name: idPerfil
          in: query
          description: ID del perfil (opcional)
          required: false
          type: integer
          format: int32
        - name: idEstado
          in: query
          description: ID del estado (opcional)
          required: false
          type: integer
          format: int32
        - name: idUsuarioSolicitante
          in: query
          description: ID del usuario solicitante (opcional)
          required: false
          type: integer
          format: int32
        - name: idTipo
          in: query
          description: ID del tipo de petición (opcional)
          required: false
          type: integer
          format: int32
        - name: fechaInicio
          in: query
          description: Fecha de inicio del filtro (formato YYYY-MM-DD) (opcional)
          required: false
          type: string
          format: date
        - name: fechaFin
          in: query
          description: Fecha de fin del filtro (formato YYYY-MM-DD) (opcional)
          required: false
          type: string
          format: date
        - name: idProceso
          in: query
          description: ID del proceso (opcional)
          required: false
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycPetiFiltro'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_reporte_peticion:
    get:
      tags:
        - Pycges
      summary: Obtener reporte de peticiones
      description: Obtiene el reporte de peticiones filtrado por área, departamento, solicitante, prioridad, analista y estado
      operationId: getReportePeticion
      produces:
        - 'application/json'
      parameters:
        - name: idArea
          in: query
          description: ID del área (opcional)
          required: false
          type: integer
          format: int32
        - name: idDepartamento
          in: query
          description: ID del departamento (opcional)
          required: false
          type: integer
          format: int32
        - name: idSolicitante
          in: query
          description: ID del usuario solicitante (opcional)
          required: false
          type: integer
          format: int32
        - name: prioridad
          in: query
          description: Prioridad de la petición (opcional, usar 'null' para peticiones sin prioridad)
          required: false
          type: string
        - name: idAnalista
          in: query
          description: ID del analista asignado (opcional)
          required: false
          type: integer
          format: int32
        - name: estado
          in: query
          description: IDs de estados separados por coma (opcional, ej. "1,2,3")
          required: false
          type: string
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycReportePeti'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/insert_user_proceso:
    post:
      tags:
        - Pycges
      summary: Insertar o actualizar usuario proceso
      description: Inserta o actualiza la relación entre un usuario y un proceso con su estado correspondiente
      operationId: insertUserProceso
      produces:
        - 'application/json'
      parameters:
        - name: userData
          in: body
          description: Datos del usuario proceso a insertar/actualizar
          required: true
          schema:
            $ref: '#/definitions/PycInsertUserPro'
      responses:
        '200':
          description: Operación exitosa
          schema:
            $ref: '#/definitions/PycUsuarioResponse'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/insert_usuario:
    post:
      tags:
        - Pycges
      summary: Insertar nuevo usuario
      description: Inserta un nuevo usuario en el sistema PYCGES con todos sus datos personales
      operationId: insertUsuario
      produces:
        - 'application/json'
      parameters:
        - name: usuarioData
          in: body
          description: Datos del usuario a insertar
          required: true
          schema:
            $ref: '#/definitions/PycInsertUsuario'
      responses:
        '200':
          description: Operación exitosa
          schema:
            $ref: '#/definitions/PycUsuarioResponse'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_obser_peticion:
    get:
      tags:
        - Pycges
      summary: Obtener observaciones de petición
      description: Obtiene la lista de observaciones asociadas a una petición específica con información del usuario y estado de conexión
      operationId: getObserPeticion
      produces:
        - 'application/json'
      parameters:
        - name: idPeticion
          in: query
          description: ID de la petición
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycObserPet'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_resumen_actividad:
    get:
      tags:
        - Pycges
      summary: Obtener resumen de actividades
      description: Obtiene el resumen estadístico de actividades de una petición específica por categoría, incluyendo totales, terminadas, pendientes y horas
      operationId: getResumenActividad
      produces:
        - 'application/json'
      parameters:
        - name: idPeticion
          in: query
          description: ID de la petición
          required: true
          type: integer
          format: int32
        - name: idCategoria
          in: query
          description: ID de la categoría del tablero
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycResumenActi'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/act_documento:
    post:
      tags:
        - Pycges
      summary: Actualizar documento de petición
      description: Actualiza la información de un documento asociado a una petición en el sistema PYCGES
      operationId: actDocumento
      consumes:
        - 'application/json'
      produces:
        - 'application/json'
      parameters:
        - name: documentoData
          in: body
          description: Datos del documento a actualizar
          required: true
          schema:
            $ref: '#/definitions/PycActDocumento'
      responses:
        '200':
          description: Operación exitosa
          schema:
            $ref: '#/definitions/PycPeticionResponse'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_peticion_avance_siguiente:
    get:
      tags:
        - Pycges
      summary: Obtener peticiones con avance siguiente
      description: Obtiene la lista de peticiones con información de su petición siguiente asociada, filtradas por proceso, área, estado, compañía y año
      operationId: getPeticionAvanceSiguiente
      produces:
        - 'application/json'
      parameters:
        - name: idProceso
          in: query
          description: ID del proceso (opcional)
          required: false
          type: integer
          format: int32
        - name: idArea
          in: query
          description: ID del área (opcional)
          required: false
          type: integer
          format: int32
        - name: estado
          in: query
          description: Lista de estados separados por comas (opcional)
          required: false
          type: string
        - name: codcia
          in: query
          description: Código de compañía (opcional)
          required: false
          type: string
        - name: anio
          in: query
          description: Año de filtro (opcional)
          required: false
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycPetAvanceSig'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_categoria_peticion:
    get:
      tags:
        - Pycges
      summary: Obtener categorías por petición
      description: Obtiene la lista de categorías de actividades asociadas a una petición específica con estadísticas de avance
      operationId: getCategoriaPeticion
      produces:
        - 'application/json'
      parameters:
        - name: idPeticion
          in: query
          description: ID de la petición
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycCategoriaPet'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_lista_val:
    get:
      tags:
        - Pycges
      summary: Obtener lista de valores por tipo y proceso
      description: Obtiene la lista de valores activos filtrados por tipo y proceso específico desde la tabla PYC_LIST_VAL
      operationId: getListaVal
      produces:
        - 'application/json'
      parameters:
        - name: tipo
          in: query
          description: Tipo de valor a consultar
          required: true
          type: string
        - name: idProceso
          in: query
          description: ID del proceso
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycListVal'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_oficinas:
    get:
      tags:
        - Pycges
      summary: Obtener oficinas por proceso y usuario
      description: Obtiene la lista de oficinas disponibles para un usuario específico en un proceso determinado, considerando el indicador de oficina del usuario
      operationId: getOficinas
      produces:
        - 'application/json'
      parameters:
        - name: idProceso
          in: query
          description: ID del proceso
          required: true
          type: integer
          format: int32
        - name: idUsuario
          in: query
          description: ID del usuario
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycOficina'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_canales:
    get:
      tags:
        - Pycges
      summary: Obtener canales por proceso, usuario y oficina
      description: Obtiene la lista de canales disponibles para un usuario específico en un proceso y oficina determinados, considerando los indicadores de canal y oficina del usuario
      operationId: getCanales
      produces:
        - 'application/json'
      parameters:
        - name: idProceso
          in: query
          description: ID del proceso
          required: true
          type: integer
          format: int32
        - name: idUsuario
          in: query
          description: ID del usuario
          required: true
          type: integer
          format: int32
        - name: idOficina
          in: query
          description: ID de la oficina
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycCanal'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_subordinados:
    get:
      tags:
        - Pycges
      summary: Obtener subordinados por canal, oficina y supervisor
      description: Obtiene la lista de subordinados disponibles para un supervisor específico considerando canal, oficina, indicador de login y proceso. Implementa lógica condicional muy compleja basada en múltiples indicadores (ind_admin, ind_oficina, ind_canal) con diferentes escenarios de acceso
      operationId: getSubordinados
      produces:
        - 'application/json'
      parameters:
        - name: canal
          in: query
          description: ID del canal
          required: true
          type: string
        - name: oficina
          in: query
          description: ID de la oficina
          required: true
          type: string
        - name: idSupervisor
          in: query
          description: ID del supervisor
          required: true
          type: string
        - name: indLogin
          in: query
          description: Indicador de login (S/N)
          required: true
          type: string
        - name: proceso
          in: query
          description: ID del proceso
          required: true
          type: string
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycSubordinados'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_info_usuario:
    get:
      tags:
        - Pycges
      summary: Obtener información completa del usuario
      description: Obtiene la información completa de un usuario específico
      operationId: getInfoUsuario
      produces:
        - 'application/json'
      parameters:
        - name: usuario
          in: query
          description: Nombre de usuario
          required: true
          type: string
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycInfoUsuario'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/crm_comercial/obtener_ramo_equiv:
    get:
      tags:
        - Pycges
      summary: Obtener información de ramo por código
      description: Obtiene la información de un ramo específico
      operationId: obtenerRamo
      produces:
        - 'application/json'
      parameters:
        - name: codRamo
          in: query
          description: Código del ramo
          required: true
          type: string
        - name: codModalidad
          in: query
          description: Código de modalidad
          required: false
          type: integer
          format: int32
        - name: codCia
          in: query
          description: Código de compañía (obligatorio)
          required: true
          type: string
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycRamo'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/crm_comercial/obtener_dat_var_equiv:
    get:
      tags:
        - Pycges
      summary: Obtener datos de variables equivalentes por ramo
      description: Obtiene los datos de variables equivalentes por ramo
      operationId: obtenerDatVarEquiv
      produces:
        - 'application/json'
      parameters:
        - name: codRamo
          in: query
          description: Código del ramo
          required: true
          type: string
        - name: codModalidad
          in: query
          description: Código de modalidad
          required: false
          type: integer
          format: int32
        - name: codCia
          in: query
          description: Código de compañía (obligatorio)
          required: true
          type: string
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycDatoVarEquiv'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/perfiles_usuario:
    get:
      tags:
        - Pycges
      summary: Obtener perfiles de usuario por ID de usuario y proceso
      description: Obtiene los perfiles asignados a un usuario
      operationId: perfilesUsuario
      produces:
        - 'application/json'
      parameters:
        - name: idUsuario
          in: query
          description: ID del usuario (obligatorio)
          required: true
          type: integer
          format: int32
        - name: idProceso
          in: query
          description: ID del proceso (obligatorio)
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycPerfilUsu'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

  /pycges/get_peticiones_tablero:
    get:
      tags:
        - Pycges
      summary: Obtener peticiones para tablero
      description: Obtiene la lista completa de peticiones con información básica para mostrar en el tablero, incluyendo datos del tipo de petición, estado y usuario solicitante
      operationId: getPeticionesTablero
      produces:
        - 'application/json'
      responses:
        '200':
          description: Operación exitosa
          schema:
            type: array
            items:
              $ref: '#/definitions/PycPeticionTab'
        '400':
          description: Bad request
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '401':
          description: Not authorized
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '403':
          description: Forbidden
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '404':
          description: Not found
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'
        '500':
          description: Uncontrolled error
          schema:
            $ref: 'API_OBJECTS.yaml#/definitions/Error'

definitions:
  Message:
    type: object
    required:
      - code
      - title
      - message
      - type
    properties:
      code:
        type: string
        description: A code that represents the result of the operation.
        example: "000"
      title:
        type: string
        description: A concise title summarizing the operation.
        example: "Operation successfully completed"
      message:
        type: string
        description: A detailed description of the operation performed.
        example: "Description of the operation performed"
      type:
        type: string
        description: The type of response provided for the operation.
        example: "success"

  TasaCambio:
    type: object
    properties:
      code:
        type: string
        description: A code that represents the result of the operation.
        example: "200"
      tasaCambio:
        type: integer
        description: Monto equivalente de tasa cambio
        example: "7.75"

  TipoDocumentoPago:
    type: object
    properties:
      noDocto:
        type: string
        description: Código del requerimiento o documento encontrado
        example: "112902"
      mcaAviso:
        type: string
        description: Indicador si es aviso ('S' o 'N')
        example: "N"
      monto:
        type: number
        format: double
        description: Monto asociado al documento
        example: 94300.00

  TipoDocumentoPagoResponse:
    type: object
    properties:
      tiposDocumento:
        type: array
        description: Lista de tipos de documentos de pago
        items:
          $ref: '#/definitions/TipoDocumentoPago'
      codigo:
        type: string
        description: Código de resultado de la operación
        example: "200"
      mensaje:
        type: string
        description: Mensaje descriptivo del resultado de la operación
        example: "Operación exitosa"

  Siniestro:
    type: object
    properties:
      siniestro:
        type: string
        description: Número de siniestro
        example: "300252001000007"
      siniestroId:
        type: string
        description: ID del siniestro
        example: "300252001000007"
      codigoFase:
        type: string
        description: Código de fase del siniestro
        example: "GS"
      numOrden:
        type: integer
        format: int32
        description: Número de orden
        example: 1
      fechaGrabacion:
        type: string
        description: Fecha de grabación del siniestro
        example: "27-APR-25"
      expediente:
        type: integer
        format: int32
        description: Número de expediente
        example: 1
      poliza:
        type: string
        description: Número de póliza
        example: "0230025018359"
      certificado:
        type: integer
        format: int32
        description: Número de certificado
        example: 1
      asegurado:
        type: string
        description: Nombre del asegurado
        example: "CASTELLANOS GARCIA HUGO LEONEL"
      correo:
        type: string
        description: Correo electrónico del asegurado
        example: ""
      placa:
        type: string
        description: Placa del vehículo
        example: "P786JRV"
      proceso:
        type: string
        description: Descripción del proceso actual
        example: "GRABACION DEL SINIESTRO"
      envioCorreo:
        type: string
        description: Indicador de envío de correo
        example: "ENV"
      tipExp:
        type: string
        description: Tipo de expediente
        example: "RCA"
      codInter:
        type: integer
        format: int32
        description: Código de intermediario
        example: 1
      agente:
        type: string
        description: Nombre del agente
        example: ""
      siniRef:
        type: string
        description: Referencia del siniestro
        example: ""
      estado:
        type: string
        description: Estado del siniestro
        example: "PENDIENTE"

  EtapaSiniestro:
    type: object
    properties:
      numOrden:
        type: integer
        format: int32
        description: Número de orden
        example: 1
      codigoFase:
        type: string
        description: Código de fase del siniestro
        example: "GS"
      codCia:
        type: integer
        format: int32
        description: Código de compañía
        example: 2
      codRamo:
        type: integer
        format: int32
        description: Código de ramo
        example: 300
      numPoliza:
        type: string
        description: Número de póliza
        example: "0230025018359"
      numSini:
        type: string
        description: Número de siniestro
        example: "300252001000007"
      deducible:
        type: number
        format: double
        description: Monto del deducible
        example: 0
      etapa:
        type: string
        description: Etapa del siniestro
        example: "GS"
      cuenta:
        type: string
        description: Número de cuenta
        example: null
      cheque:
        type: string
        description: Número de cheque
        example: null
      monto:
        type: number
        format: double
        description: Monto asociado
        example: null
      fecha:
        type: string
        description: Fecha de la etapa
        example: "27/04/2025 17:26:26"
      perito:
        type: string
        description: Nombre del perito
        example: null
      taller:
        type: string
        description: Nombre del taller
        example: null

  SiniestroNit:
    type: object
    properties:
      siniestro:
        type: string
        description: Número de siniestro
        example: "300252001000007"
      poliza:
        type: string
        description: Número de póliza
        example: "0230025018359"

  RamoSiniestro:
    type: object
    properties:
      ramo:
        type: integer
        format: int32
        description: Código de ramo del siniestro
        example: 300

  UrlSiniestro:
    type: object
    properties:
      url:
        type: string
        description: URL encriptada para acceder al siniestro
        example: "http://localhost/siniestro?token=abc123def456"

  DetalleSiniestro:
    type: object
    properties:
      numOrden:
        type: integer
        format: int32
        description: Número de orden
        example: 1
      codEtapa:
        type: string
        description: Código de etapa del siniestro
        example: "GRABACION DEL SINIESTRO"
      descripcion:
        type: string
        description: Descripción detallada de la etapa
        example: "Tu siniestro ha sido aperturado"
      fecha:
        type: string
        description: Fecha de la etapa
        example: "05/05/2025 21:11:20"
      etapa:
        type: string
        description: Nombre de la etapa
        example: "Apertura Siniestro"
      imagen:
        type: string
        description: URL de la imagen asociada a la etapa
        example: "https://app2.mapfre.com.gt/CDN/images/mapfre/tracking/reclamos/autos/apertura.png"
      subTracking:
        type: string
        description: Indicador de sub-tracking
        example: "N"

  EncuestaSiniestro:
    type: object
    properties:
      idEncuesta:
        type: integer
        format: int32
        description: Identificador de la encuesta
        example: 1
      encuesta:
        type: string
        description: Nombre de la encuesta
        example: "Encuesta de satisfacción"
      descripcion:
        type: string
        description: Descripción de la encuesta
        example: "Encuesta para evaluar la satisfacción del cliente"
      idPregunta:
        type: integer
        format: int32
        description: Identificador de la pregunta
        example: 1
      pregunta:
        type: string
        description: Texto de la pregunta
        example: "¿Cómo calificaría nuestro servicio?"
      idRespuesta:
        type: integer
        format: int32
        description: Identificador de la respuesta
        example: 1
      respuesta:
        type: string
        description: Texto de la respuesta
        example: "Excelente"

  EncuestaSiniestroRequest:
    type: object
    required:
      - numSiniestro
      - idEncuesta
      - archXml
    properties:
      numSiniestro:
        type: string
        description: Número de siniestro
        example: "300252001000007"
      idEncuesta:
        type: integer
        format: int32
        description: Identificador de la encuesta
        example: 1
      archXml:
        type: string
        description: XML con las respuestas de la encuesta
        example: "<Encuesta><listado><id_pregunta>1</id_pregunta><id_respuesta>2</id_respuesta></listado></Encuesta>"

  EncuestaSiniestroResponse:
    type: object
    properties:
      resultado:
        type: string
        description: Resultado de la operación
        example: "OK"

  ValidaIngresoEncuestaResponse:
    type: object
    properties:
      resultado:
        type: string
        description: Resultado de la validación
        example: "OK"

  SiniestroVehiculo:
    type: object
    properties:
      codEstado:
        type: string
        description: Código de estado del siniestro
        example: "TR01"
      nomEstado:
        type: string
        description: Nombre del estado del siniestro
        example: "Recepción del vehículo"
      jsonArchivos:
        type: string
        description: JSON con información de archivos asociados
        example: "{}"
      fechaActualizacion:
        type: string
        description: Fecha de actualización del estado
        example: "05/05/2025 21:11:20"
      imagen:
        type: string
        description: URL de la imagen asociada al estado
        example: "https://ejemplo.com/imagen.png"

  PycUsuario:
    type: object
    properties:
      idUsuario:
        type: integer
        format: int32
        description: Identificador del usuario
        example: 1234
      nombre:
        type: string
        description: Nombre del usuario
        example: "Juan Pérez"
      fechaCreacion:
        type: string
        description: Fecha de creación del usuario
        example: "2025-05-21T14:20:00Z"
      estado:
        type: string
        description: Estado del usuario
        example: "ACT"
      fechaBaja:
        type: string
        description: Fecha de baja del usuario (si aplica)
        example: "2025-12-31T00:00:00Z"
      usuarioBaseDatos:
        type: string
        description: Nombre de usuario en la base de datos
        example: "jperezdb"
      email:
        type: string
        description: Correo electrónico del usuario
        example: "<EMAIL>"
      genero:
        type: string
        description: Género del usuario
        example: "M"

  PycAplicacion:
    type: object
    properties:
      idAplicacion:
        type: integer
        format: int32
        description: Identificador de la aplicación
        example: 1
      nombreAplicacion:
        type: string
        description: Nombre de la aplicación
        example: "Aplicación de Gestión"

  PycPerfil:
    type: object
    properties:
      idPerfil:
        type: integer
        format: int32
        description: Identificador del perfil
        example: 1
      nombrePerfil:
        type: string
        description: Nombre del perfil
        example: "Administrador"
      descripcionPerfil:
        type: string
        description: Descripción del perfil
        example: "Perfil con acceso completo al sistema"
      usuario:
        type: string
        description: Usuario que creó o modificó el perfil
        example: "jperez"
      fechaHora:
        type: string
        description: Fecha y hora de creación o última modificación
        example: "2025-05-21T14:20:00Z"

  PycUsuarioPerfil:
    type: object
    properties:
      nombre:
        type: string
        description: Nombre completo del usuario (primer nombre y primer apellido)
        example: "Juan Pérez"
      nombrePerfil:
        type: string
        description: Nombre del perfil
        example: "Administrador"
      idUsuario:
        type: integer
        format: int32
        description: Identificador del usuario
        example: 1234
      idPerfil:
        type: integer
        format: int32
        description: Identificador del perfil
        example: 1
      multiperfil:
        type: string
        description: Indicador si el usuario tiene múltiples perfiles
        example: "S"
      indDefault:
        type: string
        description: Indicador si es el perfil por defecto
        example: "S"

  PycListadoAsigna:
    type: object
    properties:
      idUsuario:
        type: integer
        format: int32
        description: Identificador del usuario
        example: 1234
      nombre:
        type: string
        description: Nombre completo del usuario (primer nombre y primer apellido)
        example: "Juan Pérez"
      idPerfil:
        type: integer
        format: int32
        description: Identificador del perfil
        example: 1
      nombrePerfil:
        type: string
        description: Nombre del perfil
        example: "Administrador"

  PycProcesoPorTipo:
    type: object
    properties:
      idProceso:
        type: integer
        format: int32
        description: Identificador del proceso
        example: 1
      nombreProceso:
        type: string
        description: Nombre del proceso
        example: "Proceso de Gestión"

  PycGetQueryDatVar:
    type: object
    properties:
      query:
        type: string
        description: Consulta SQL asociada al dato variable
        example: "SELECT * FROM tabla WHERE condicion = 1"

  PycSrcMultipleOption:
    type: object
    properties:
      hasMultipleOption:
        type: integer
        format: int32
        description: Indica si el perfil tiene la opción de selección múltiple (1 = sí)
        example: 1

  PycGetUsuariosAsig:
    type: object
    properties:
      idUsuario:
        type: integer
        format: int32
        description: Identificador del usuario
        example: 1234
      idProceso:
        type: integer
        format: int32
        description: Identificador del proceso
        example: 1
      nombre:
        type: string
        description: Nombre completo del usuario
        example: "Juan Carlos Pérez"
      idAplicacion:
        type: integer
        format: int32
        description: Identificador de la aplicación
        example: 2
      idPerfil:
        type: integer
        format: int32
        description: Identificador del perfil
        example: 3
      idEstado:
        type: integer
        format: int32
        description: Identificador del estado
        example: 1
      indAutomatico:
        type: string
        description: Indicador de asignación automática
        example: "S"
      indAdmin:
        type: string
        description: Indicador de administrador
        example: "S"
      urlPerfil:
        type: string
        description: URL de la imagen de perfil del usuario
        example: "https://ejemplo.com/perfil/usuario.jpg"

  PycUsuPerfilProceso:
    type: object
    properties:
      idUsuario:
        type: integer
        format: int32
        description: Identificador del usuario
        example: 1234
      nombre:
        type: string
        description: Nombre completo del usuario
        example: "Juan Pérez"
      indDefault:
        type: string
        description: Indicador si es el usuario por defecto
        example: "S"

  PycUsuProcesos:
    type: object
    properties:
      idUsuario:
        type: integer
        format: int32
        description: Identificador del usuario
        example: 1234
      nombreUsuario:
        type: string
        description: Nombre completo del usuario
        example: "Juan Pérez"
      usuario:
        type: string
        description: Nombre de usuario en la base de datos
        example: "jperez"
      idProceso:
        type: integer
        format: int32
        description: Identificador del proceso
        example: 1
      nombreProceso:
        type: string
        description: Nombre del proceso
        example: "Gestión de Peticiones"
      estado:
        type: string
        description: Estado del proceso para el usuario
        example: "ACT"
      icono:
        type: string
        description: Icono asociado al proceso
        example: "fa-tasks"

  PycUpdPerfil:
    type: object
    required:
      - idPerfil
      - idUsuario
    properties:
      idPerfil:
        type: integer
        format: int32
        description: Identificador del perfil
        example: 1
      idUsuario:
        type: integer
        format: int32
        description: Identificador del usuario
        example: 1234
      indDefault:
        type: string
        description: Indicador si es el perfil por defecto
        example: "S"
      multiperfil:
        type: string
        description: Indicador si el usuario puede tener múltiples perfiles
        example: "S"
      estado:
        type: string
        description: Estado del perfil para el usuario
        example: "ACT"

  PycUpdUsuario:
    type: object
    required:
      - primerNombre
      - primerApellido
      - usuarioBaseDatos
      - estado
    properties:
      primerNombre:
        type: string
        description: Primer nombre del usuario
        example: "Juan"
      segundoNombre:
        type: string
        description: Segundo nombre del usuario
        example: "Carlos"
      primerApellido:
        type: string
        description: Primer apellido del usuario
        example: "Pérez"
      segundoApellido:
        type: string
        description: Segundo apellido del usuario
        example: "García"
      usuarioBaseDatos:
        type: string
        description: Nombre de usuario en la base de datos
        example: "jperez"
      estado:
        type: string
        description: Estado del usuario (ACT, INA)
        example: "ACT"
      email:
        type: string
        description: Correo electrónico del usuario
        example: "<EMAIL>"
      genero:
        type: string
        description: Género del usuario (M, F)
        example: "M"
      idArea:
        type: integer
        format: int32
        description: Identificador del área a la que pertenece el usuario
        example: 1
      idDepartamento:
        type: integer
        format: int32
        description: Identificador del departamento al que pertenece el usuario
        example: 2

  PycUsuarioResponse:
    type: object
    properties:
      idUsuario:
        type: integer
        format: int32
        description: ID del usuario actualizado o creado

  PycSolicitudResponse:
    type: object
    properties:
      idSolicitud:
        type: integer
        format: int32
        description: ID de la solicitud creada

  PycPeticionResponse:
    type: object
    properties:
      idPeticion:
        type: integer
        format: int32
        description: ID de la petición creada

  PycCreaSolicitud:
    type: object
    required:
      - plataforma
      - tipoSeguro
      - nombres
      - apellidos
      - telefono
      - email
    properties:
      plataforma:
        type: string
        description: Plataforma desde la que se crea la solicitud
        example: "COTIZADOR"
      tipoSeguro:
        type: string
        description: Tipo de seguro solicitado
        example: "Automóvil"
      nombres:
        type: string
        description: Nombres del cliente
        example: "Juan Carlos"
      apellidos:
        type: string
        description: Apellidos del cliente
        example: "Pérez García"
      telefono:
        type: string
        description: Número de teléfono del cliente
        example: "55123456"
      email:
        type: string
        description: Correo electrónico del cliente
        example: "<EMAIL>"
      xmlText:
        type: string
        description: "Datos adicionales de la solicitud en formato XML. Soporta XML directo o envuelto en CDATA para preservar formato. Ejemplos: '<INFO_PETICION></INFO_PETICION>' o '<![CDATA[<INFO_PETICION>          </INFO_PETICION>]]>'"
        example: "<INFO_PETICION></INFO_PETICION>"
      codinter:
        type: string
        description: Código de intermediario
        example: "900000"

  PycInsertPeticion:
    type: object
    required:
      - nombrePeticion
      - descripcionPeticion
      - idUsuarioSolicitante
      - idPerfil
      - idTipo
      - idProceso
      - codcia
    properties:
      nombrePeticion:
        type: string
        description: Nombre de la petición
        example: "Solicitud de cambio de datos"
      descripcionPeticion:
        type: string
        description: Descripción detallada de la petición
        example: "Solicitud para cambiar datos personales en la póliza"
      idUsuarioSolicitante:
        type: integer
        format: int32
        description: ID del usuario solicitante
        example: 1234
      idPerfil:
        type: integer
        format: int32
        description: ID del perfil
        example: 1
      idTipo:
        type: integer
        format: int32
        description: ID del tipo de petición
        example: 2
      idProceso:
        type: integer
        format: int32
        description: ID del proceso
        example: 3
      codcia:
        type: string
        description: Código de compañía
        example: 2
      nombreCliente:
        type: string
        description: Nombre del cliente
        example: "Juan Carlos Pérez García"
      telefonoCliente:
        type: string
        description: Teléfono del cliente
        example: "22345678"
      correoCliente:
        type: string
        description: Correo electrónico del cliente
        example: "<EMAIL>"
      origen:
        type: string
        description: Origen de la petición
        example: "CO257"
      codClarity:
        type: string
        description: Código Clarity
        example: "MU-2019-038493"
      prioridad:
        type: string
        description: Prioridad de la petición
        example: "1"
      tipoCliente:
        type: string
        description: Tipo de cliente
        example: "2"
      codCliente:
        type: string
        description: Código del cliente
        example: "12345678"
      noPoliza:
        type: string
        description: Número de póliza
        example: "0230025018359"
      tipServicio:
        type: string
        description: Tipo de servicio
        example: "1"
      causa:
        type: string
        description: Causa de la petición
        example: "1"
      gravedad:
        type: string
        description: Gravedad de la petición
        example: "1"
      idReferencia:
        type: string
        description: ID de referencia
        example: "1"
      codinter:
        type: string
        description: Código de intermediario
        example: "900000"
      xmlText:
        type: string
        description: "Datos adicionales en formato XML. Soporta XML directo o envuelto en CDATA para preservar formato. Ejemplos: '<INFO_PETICION></INFO_PETICION>' o '<![CDATA[<INFO_PETICION>          </INFO_PETICION>]]>'"
        example: "<INFO_PETICION></INFO_PETICION>"
      usuarioReg:
        type: string
        description: Usuario que registra la petición
        example: "ADMIN"
      asigAuto:
        type: boolean
        description: Indicador de asignación automática
        example: true

  PycPeticionPerfil:
    type: object
    properties:
      idPeticion:
        type: integer
        format: int32
        description: Identificador de la petición
        example: 1001
      nombrePeticion:
        type: string
        description: Nombre de la petición
        example: "Solicitud de cambio de datos"
      descripcionPeticion:
        type: string
        description: Descripción detallada de la petición
        example: "Solicitud para cambiar datos personales en la póliza"
      idPerfil:
        type: integer
        format: int32
        description: Identificador del perfil
        example: 1
      nombrePerfil:
        type: string
        description: Nombre del perfil
        example: "Administrador"
      idProceso:
        type: integer
        format: int32
        description: Identificador del proceso
        example: 3
      nombreProceso:
        type: string
        description: Nombre del proceso
        example: "Gestión de Peticiones"
      idEstado:
        type: string
        description: Identificador del estado
        example: "PEN"
      nombreEstado:
        type: string
        description: Nombre del estado
        example: "Pendiente"
      fechaCreacion:
        type: string
        description: Fecha de creación de la petición
        example: "2025-05-21T14:20:00Z"
      idUsuarioSolicitante:
        type: integer
        format: int32
        description: Identificador del usuario solicitante
        example: 1234
      nombreUsuarioSolicitante:
        type: string
        description: Nombre del usuario solicitante
        example: "Juan Pérez"
      idCanal:
        type: integer
        format: int32
        description: Identificador del canal
        example: 1
      nombreCanal:
        type: string
        description: Nombre del canal
        example: "Web"
      idOficina:
        type: integer
        format: int32
        description: Identificador de la oficina
        example: 1
      nombreOficina:
        type: string
        description: Nombre de la oficina
        example: "Oficina Central"
      prioridad:
        type: string
        description: Prioridad de la petición
        example: "Alta"
      nombreCliente:
        type: string
        description: Nombre del cliente
        example: "Juan Carlos Pérez García"
      telefonoCliente:
        type: string
        description: Teléfono del cliente
        example: "22345678"
      correoCliente:
        type: string
        description: Correo electrónico del cliente
        example: "<EMAIL>"

  PycPetSinProgramador:
    type: object
    properties:
      idPeticion:
        type: integer
        format: int32
        description: Identificador de la petición
        example: 1001
      tipoPeticion:
        type: string
        description: Tipo de petición
        example: "Solicitud"
      nombrePeticion:
        type: string
        description: Nombre de la petición
        example: "Solicitud de cambio de datos"
      descripcionPeticion:
        type: string
        description: Descripción detallada de la petición
        example: "Solicitud para cambiar datos personales en la póliza"
      fechaCreacion:
        type: string
        description: Fecha de creación de la petición (formato DD/MM/YYYY)
        example: "21/05/2025"
      idTipo:
        type: integer
        format: int32
        description: Identificador del tipo
        example: 1
      idUsuarioSolicitante:
        type: integer
        format: int32
        description: Identificador del usuario solicitante
        example: 1234
      usuario:
        type: string
        description: Nombre del usuario
        example: "Juan Pérez"
      fechaInicio:
        type: string
        description: Fecha de inicio
        example: "2025-05-21T14:20:00Z"
      fechaFin:
        type: string
        description: Fecha de fin
        example: "2025-05-28T14:20:00Z"
      totalHoras:
        type: number
        format: double
        description: Total de horas estimadas
        example: 40.5
      porcentajeBase:
        type: number
        format: double
        description: Porcentaje base
        example: 75.0
      porcentajeReal:
        type: number
        format: double
        description: Porcentaje real
        example: 80.0
      idEstado:
        type: integer
        format: int32
        description: Identificador del estado
        example: 1
      nombreEstado:
        type: string
        description: Nombre del estado
        example: "Pendiente"
      colorEstado:
        type: string
        description: Color asociado al estado
        example: "#FF5733"
      idPerfil:
        type: integer
        format: int32
        description: Identificador del perfil
        example: 1
      nombrePerfil:
        type: string
        description: Nombre del perfil
        example: "Administrador"
      usuarioGraba:
        type: string
        description: Usuario que grabó la petición
        example: "admin"
      usuarioSoli:
        type: string
        description: Usuario solicitante
        example: "jperez"
      origen:
        type: string
        description: Origen de la petición
        example: "CO257"
      codClarity:
        type: string
        description: Código Clarity
        example: "MU-2019-038493"
      codcia:
        type: string
        description: Código de compañía
        example: "2"
      prioridad:
        type: string
        description: Prioridad de la petición
        example: "1"
      prioriDesc:
        type: string
        description: Descripción de la prioridad
        example: "Alta"
      idPeticionSiguiente:
        type: integer
        format: int32
        description: Identificador de la petición siguiente
        example: 1002

  PycAreaPeti:
    type: object
    properties:
      idArea:
        type: integer
        format: int32
        description: Identificador del área
        example: 1
      nombreArea:
        type: string
        description: Nombre del área
        example: "Área de Desarrollo"

  PycAreaUsuario:
    type: object
    properties:
      idUsuario:
        type: integer
        format: int32
        description: Identificador del usuario
        example: 1234
      usuario:
        type: string
        description: Nombre completo del usuario (primer nombre y primer apellido)
        example: "Juan Pérez"

  PycDepartamento:
    type: object
    properties:
      idDepartamento:
        type: integer
        format: int32
        description: Identificador del departamento
        example: 1
      nombreDepartamento:
        type: string
        description: Nombre del departamento
        example: "Desarrollo de Software"

  PycAnalista:
    type: object
    properties:
      idUsuario:
        type: integer
        format: int32
        description: Identificador del usuario analista
        example: 1234
      usuario:
        type: string
        description: Nombre completo del analista (primer nombre y primer apellido)
        example: "María García"

  PycConEstado:
    type: object
    properties:
      idEstado:
        type: integer
        format: int32
        description: Identificador del estado
        example: 1
      nombreEstado:
        type: string
        description: Nombre del estado
        example: "Pendiente"

  PycPrioridad:
    type: object
    properties:
      codigo:
        type: string
        description: Código de la prioridad
        example: "1"
      prioridad:
        type: string
        description: Descripción de la prioridad
        example: "Alta"

  PycTabAnios:
    type: object
    properties:
      anios:
        type: string
        description: Año de las peticiones en formato YYYY
        example: "2024"

  PycTabArea:
    type: object
    properties:
      idArea:
        type: integer
        format: int32
        description: Identificador del área
        example: 1
      nombreArea:
        type: string
        description: Nombre del área
        example: "Tecnología"

  PycTabEstado:
    type: object
    properties:
      idEstado:
        type: integer
        format: int32
        description: Identificador del estado
        example: 1
      nombreEstado:
        type: string
        description: Nombre del estado
        example: "Pendiente"

  PycPetAvance:
    type: object
    properties:
      idPeticion:
        type: integer
        format: int32
        description: Identificador de la petición
        example: 1001
      nombrePeticion:
        type: string
        description: Nombre de la petición
        example: "Desarrollo de nueva funcionalidad"
      fechaInicio:
        type: string
        description: Fecha de inicio de la petición
        example: "15/01/2024"
      fechaFin:
        type: string
        description: Fecha de fin de la petición
        example: "20/02/2024"
      nombreEstado:
        type: string
        description: Nombre del estado actual
        example: "En Progreso"
      idPeticionSiguiente:
        type: integer
        format: int32
        description: Identificador de la petición siguiente
        example: 1002
      nombrePeticionSiguiente:
        type: string
        description: Nombre de la petición siguiente
        example: "Pruebas de funcionalidad"
      fechaInicioSiguiente:
        type: string
        description: Fecha de inicio de la petición siguiente
        example: "21/02/2024"
      fechaFinSiguiente:
        type: string
        description: Fecha de fin de la petición siguiente
        example: "28/02/2024"
      idObservacion:
        type: integer
        format: int32
        description: Identificador de la observación
        example: 123
      observacion:
        type: string
        description: Texto de la observación
        example: "Avance del 75% completado"
      fechaHoraObservacion:
        type: string
        description: Fecha y hora de la observación (formato dd/MM/yyyy HH24:MI)
        example: "15/01/2024 14:30"
      nombreUsuario:
        type: string
        description: Nombre completo del usuario que hizo la observación
        example: "Juan Pérez"
      generoUsuario:
        type: string
        description: Género del usuario (M/F)
        example: "M"
      indicadorUsuario:
        type: string
        description: Indicador de posición del usuario ('right'/'left')
        example: "left"
      urlPerfil:
        type: string
        description: URL del perfil del usuario o imagen por defecto
        example: "/images/profile/user_male.png"
      tipoPeticion:
        type: string
        description: Tipo de petición
        example: "Desarrollo"
      prioridad:
        type: string
        description: Descripción de la prioridad
        example: "Alta"
      solicitante:
        type: string
        description: Nombre completo del usuario solicitante
        example: "María García"

  PycUsuPerfilPeti:
    type: object
    properties:
      idUsuario:
        type: integer
        format: int32
        description: Identificador del usuario
        example: 1234
      nombre:
        type: string
        description: Nombre completo del usuario (primer nombre, segundo nombre y primer apellido)
        example: "Juan Carlos Pérez"
      genero:
        type: string
        description: Género del usuario
        example: "M"
      urlPerfil:
        type: string
        description: URL de la imagen de perfil del usuario
        example: "/images/user_male.png"
      idPerfil:
        type: integer
        format: int32
        description: Identificador del perfil
        example: 5
      nombrePerfil:
        type: string
        description: Nombre del perfil
        example: "Analista Senior"
      asigna:
        type: string
        description: Indicador si el usuario está asignado (1) o no (0)
        example: "1"

  PycPetiFiltro:
    type: object
    properties:
      idPeticion:
        type: integer
        format: int32
        description: Identificador de la petición
        example: 1001
      nombrePeticion:
        type: string
        description: Nombre de la petición
        example: "Desarrollo de nueva funcionalidad"
      descripcionPeticion:
        type: string
        description: Descripción detallada de la petición
        example: "Implementación de módulo de reportes"
      fechaCreacion:
        type: string
        description: Fecha de creación de la petición
        example: "2024-01-15T10:30:00Z"
      idTipo:
        type: integer
        format: int32
        description: Identificador del tipo de petición
        example: 1
      idUsuarioSolicitante:
        type: integer
        format: int32
        description: Identificador del usuario solicitante
        example: 1234
      usuario:
        type: string
        description: Nombre completo del usuario solicitante
        example: "Juan Pérez"
      fechaInicio:
        type: string
        description: Fecha de inicio de la petición
        example: "2024-01-20T08:00:00Z"
      fechaFin:
        type: string
        description: Fecha de fin de la petición
        example: "2024-02-20T18:00:00Z"
      totalHoras:
        type: number
        format: double
        description: Total de horas estimadas
        example: 120.5
      porcentajeBase:
        type: number
        format: double
        description: Porcentaje base de la petición
        example: 85.0
      porcentajeReal:
        type: number
        format: double
        description: Porcentaje real de avance
        example: 75.5
      idEstado:
        type: integer
        format: int32
        description: Identificador del estado
        example: 2
      idPerfil:
        type: integer
        format: int32
        description: Identificador del perfil
        example: 5
      nombreDepartamento:
        type: string
        description: Nombre del departamento
        example: "Desarrollo de Software"
      nombreArea:
        type: string
        description: Nombre del área
        example: "Tecnología"
      nombrePerfil:
        type: string
        description: Nombre del perfil
        example: "Analista Senior"
      nombreEstado:
        type: string
        description: Nombre del estado
        example: "En Progreso"
      descripcionTipoPeticion:
        type: string
        description: Descripción del tipo de petición
        example: "Desarrollo"
      origen:
        type: string
        description: Origen de la petición
        example: "Cliente Interno"
      codClarity:
        type: string
        description: Código Clarity
        example: "MU-2024-001234"
      codcia:
        type: string
        description: Código de compañía
        example: "2"
      prioridad:
        type: string
        description: Prioridad de la petición
        example: "1"
      idPeticionSiguiente:
        type: integer
        format: int32
        description: Identificador de la petición siguiente
        example: 1002

  PycReportePeti:
    type: object
    properties:
      idPeticion:
        type: integer
        format: int32
        description: Identificador de la petición
        example: 1001
      nombrePeticion:
        type: string
        description: Nombre de la petición
        example: "Solicitud de cambio de datos"
      estado:
        type: string
        description: Id estado
        example: "1"
      fechaInicio:
        type: string
        description: Fecha de inicio de la petición (formato DD/MM/YYYY)
        example: "15/01/2024"
      fechaFin:
        type: string
        description: Fecha de fin de la petición (formato DD/MM/YYYY)
        example: "20/02/2024"
      prioridad:
        type: string
        description: Descripción de la prioridad de la petición
        example: "Alta"
      usuario:
        type: string
        description: Nombre completo del usuario solicitante (primer nombre y primer apellido)
        example: "Juan Pérez"
      analista:
        type: string
        description: Nombre completo del analista asignado (primer nombre y primer apellido)
        example: "María García"
      codClarity:
        type: string
        description: Código Clarity de la petición
        example: "MU-2024-001234"
      nombreArea:
        type: string
        description: Nombre del área
        example: "Tecnología"
      nombreDepartamento:
        type: string
        description: Nombre del departamento
        example: "Desarrollo de Software"
      observaciones:
        type: string
        description: Última observación registrada para la petición
        example: "Petición en proceso de revisión"

  PycInsertUserPro:
    type: object
    required:
      - idUsuario
      - idProceso
      - estado
    properties:
      idUsuario:
        type: integer
        format: int32
        description: Identificador del usuario
        example: 123
      idProceso:
        type: integer
        format: int32
        description: Identificador del proceso
        example: 456
      estado:
        type: string
        description: Estado de la relación usuario-proceso (ACT/INA)
        example: "ACT"

  PycInsertUsuario:
    type: object
    required:
      - primerNombre
      - primerApellido
      - usuarioBaseDatos
      - email
      - genero
    properties:
      primerNombre:
        type: string
        description: Primer nombre del usuario
        example: "Juan"
      segundoNombre:
        type: string
        description: Segundo nombre del usuario (opcional)
        example: "Carlos"
      primerApellido:
        type: string
        description: Primer apellido del usuario
        example: "Pérez"
      segundoApellido:
        type: string
        description: Segundo apellido del usuario (opcional)
        example: "García"
      usuarioBaseDatos:
        type: string
        description: Usuario de base de datos
        example: "jperez"
      email:
        type: string
        format: email
        description: Correo electrónico del usuario
        example: "<EMAIL>"
      genero:
        type: string
        description: Género del usuario (M/F)
        example: "M"

  PycActDocumento:
    type: object
    required:
      - idPeticion
      - idDocumento
      - localizacion
    properties:
      idPeticion:
        type: integer
        format: int32
        description: Identificador de la petición
        example: 1001
      idDocumento:
        type: integer
        format: int32
        description: Identificador del documento
        example: 5
      localizacion:
        type: string
        description: Localización del documento
        example: "/documentos/peticion_1001/documento_5.pdf"
      documento:
        type: string
        description: Nombre del documento (opcional)
        example: "Documento de soporte"
      ftpWeb:
        type: string
        description: Indicador de FTP/Web (S/N, por defecto 'N')
        example: "N"
      nomArch:
        type: string
        description: Nombre del archivo (opcional)
        example: "soporte_documento.pdf"

  PycObserPet:
    type: object
    properties:
      publico:
        type: string
        description: Indicador si la observación es pública (S/N)
        example: "S"
      urlPerfil:
        type: string
        description: URL del perfil del usuario o imagen por defecto según género
        example: "/images/profile/user_male.png"
      idPeticion:
        type: integer
        format: int32
        description: Identificador de la petición
        example: 1001
      nombre:
        type: string
        description: Nombre completo del usuario (primer nombre + primer apellido)
        example: "Juan Pérez"
      nombrePeticion:
        type: string
        description: Nombre de la petición
        example: "Solicitud de cambio de datos"
      idObservacion:
        type: integer
        format: int32
        description: Identificador único de la observación
        example: 123
      observacion:
        type: string
        description: Texto de la observación en mayúsculas
        example: "SE REQUIERE DOCUMENTACIÓN ADICIONAL"
      fechaHora:
        type: string
        description: Fecha y hora formateada de manera amigable (ej. 'Hace un momento', 'Hoy 14:30', 'Ayer 09:15')
        example: "Hoy 14:30"
      nombreArea:
        type: string
        description: Nombre del área del usuario
        example: "Tecnología"
      nombreDepartamento:
        type: string
        description: Nombre del departamento del usuario
        example: "Desarrollo de Software"
      estado:
        type: string
        description: Estado de la observación
        example: "ACT"
      genero:
        type: string
        description: Género del usuario (M/F)
        example: "M"
      usuario:
        type: string
        description: Usuario de base de datos
        example: "jperez"
      indicador:
        type: string
        description: Indicador de posición del mensaje ('right' si es del usuario actual, 'left' si es de otro)
        example: "left"
      connected:
        type: string
        description: Estado de conexión del usuario ('on' si está conectado, 'off' si está desconectado)
        example: "on"

  PycResumenActi:
    type: object
    properties:
      totales:
        type: integer
        format: int32
        description: Número total de actividades
        example: 15
      terminadas:
        type: integer
        format: int32
        description: Número de actividades terminadas (estado 'TER')
        example: 8
      pendientes:
        type: integer
        format: int32
        description: Número de actividades pendientes (total - terminadas)
        example: 7
      base:
        type: number
        format: double
        description: Total de horas base planificadas
        example: 120.5
      rea:
        type: number
        format: double
        description: Total de horas reales ejecutadas
        example: 135.75
      porcentaje:
        type: integer
        format: int32
        description: Porcentaje de avance (horas reales / horas base * 100)
        example: 113

  PycPetAvanceSig:
    type: object
    properties:
      idPeticion:
        type: integer
        format: int32
        description: Identificador de la petición principal
        example: 1001
      nombrePeticion:
        type: string
        description: Nombre de la petición principal
        example: "Solicitud de cambio de datos"
      fechaInicio:
        type: string
        description: Fecha de inicio de la petición principal
        example: "2025-01-15"
      fechaFin:
        type: string
        description: Fecha de fin de la petición principal
        example: "2025-01-20"
      nombreEstado:
        type: string
        description: Nombre del estado de la petición principal
        example: "En Proceso"
      idPeticionSiguiente:
        type: integer
        format: int32
        description: Identificador de la petición siguiente
        example: 1002
      nombrePeticionSiguiente:
        type: string
        description: Nombre de la petición siguiente
        example: "Validación de cambio de datos"
      fechaInicioSiguiente:
        type: string
        description: Fecha de inicio de la petición siguiente
        example: "2025-01-21"
      fechaFinSiguiente:
        type: string
        description: Fecha de fin de la petición siguiente
        example: "2025-01-25"
      nombreEstadoSiguiente:
        type: string
        description: Nombre del estado de la petición siguiente
        example: "Pendiente"

  PycListVal:
    type: object
    properties:
      valor:
        type: string
        description: Valor del elemento de la lista
        example: "001"
      descripcion:
        type: string
        description: Descripción del valor
        example: "Opción número uno"

  PycOficina:
    type: object
    properties:
      idOficina:
        type: integer
        format: int32
        description: Identificador de la oficina
        example: 1
      nombre:
        type: string
        description: Nombre de la oficina
        example: "Oficina Central"

  PycCanal:
    type: object
    properties:
      codigo:
        type: integer
        format: int32
        description: Código identificador del canal
        example: 1
      canal:
        type: string
        description: Nombre del canal
        example: "Canal Web"

  PycSubordinados:
    type: object
    properties:
      idUsuario:
        type: integer
        format: int32
        description: Identificador del usuario subordinado
        example: 1234
      subordinado:
        type: string
        description: Nombre completo del subordinado (concatenación de nombres y apellidos)
        example: "Juan Carlos Pérez González"
      nsubs:
        type: string
        description: Número de subordinados o indicador de validación
        example: "S"

  PycInfoUsuario:
    type: object
    properties:
      idUsuario:
        type: integer
        format: int32
        description: Identificador del usuario
        example: 1234
      primerNombre:
        type: string
        description: Primer nombre del usuario
        example: "Juan"
      segundoNombre:
        type: string
        description: Segundo nombre del usuario
        example: "Carlos"
      primerApellido:
        type: string
        description: Primer apellido del usuario
        example: "Pérez"
      segundoApellido:
        type: string
        description: Segundo apellido del usuario
        example: "González"
      fechaCreacion:
        type: string
        description: Fecha de creación del usuario
        example: "2025-01-15T10:30:00Z"
      estado:
        type: string
        description: Estado del usuario
        example: "ACT"
      fechaBaja:
        type: string
        description: Fecha de baja del usuario (si aplica)
        example: "2025-12-31T00:00:00Z"
      usuarioBaseDatos:
        type: string
        description: Nombre de usuario en la base de datos
        example: "jperez"
      email:
        type: string
        description: Correo electrónico del usuario
        example: "<EMAIL>"
      genero:
        type: string
        description: Género del usuario
        example: "M"
      idArea:
        type: integer
        format: int32
        description: Identificador del área
        example: 5
      area:
        type: string
        description: Nombre del área
        example: "Tecnología"
      idDepartamento:
        type: integer
        format: int32
        description: Identificador del departamento
        example: 10
      departamento:
        type: string
        description: Nombre del departamento
        example: "Desarrollo de Software"
      idPerfil:
        type: integer
        format: int32
        description: Identificador del perfil
        example: 3
      nombrePerfil:
        type: string
        description: Nombre del perfil
        example: "Desarrollador"
      indDefault:
        type: string
        description: Indicador si es el perfil por defecto
        example: "S"
      idProceso:
        type: integer
        format: int32
        description: Identificador del proceso
        example: 7
      urlPerfil:
        type: string
        description: URL del perfil del usuario
        example: "https://ejemplo.com/perfil/jperez"
      pathPerfil:
        type: string
        description: Ruta del perfil del usuario
        example: "/perfiles/jperez"
      urlInicio:
        type: string
        description: URL de inicio del usuario
        example: "https://ejemplo.com/dashboard"

  PycRamo:
    type: object
    properties:
      codCia:
        type: string
        required: true
        description: Código de la compañía
        example: "2"
      codModalidad:
        type: string
        description: Código de modalidad
        example: "999"
      codRamo:
        type: string
        description: Código del ramo
        example: "300"
      idTipoPeticion:
        type: integer
        format: int32
        description: Identificador del tipo de petición
        example: 1
      descripcionRamo:
        type: string
        description: Descripción del ramo
        example: "Automóviles"
      estado:
        type: string
        description: Estado del ramo
        example: "ACT"

  PycDatoVarEquiv:
    type: object
    properties:
      codCia:
        type: string
        required: true
        description: Código de la compañía
        example: "2"
      codModalidad:
        type: integer
        format: int32
        description: Código de modalidad
        example: 999
      codRamo:
        type: string
        description: Código del ramo
        example: "300"
      idFormulario:
        type: integer
        format: int32
        description: Identificador del formulario
        example: 1
      idSeccion:
        type: integer
        format: int32
        description: Identificador de la sección
        example: 2
      datVarId:
        type: string
        description: Identificador del dato variable
        example: "DAT_VAR_ID_001"
      datVar:
        type: string
        description: Dato variable
        example: "VARIABLE_001"
      datVarNm:
        type: string
        description: Nombre del dato variable
        example: "NOMBRE_VARIABLE_001"
      datVarReef:
        type: string
        description: Dato variable REEF
        example: "VARIABLE_REEF_001"

  PycPerfilUsu:
    type: object
    properties:
      idPerfil:
        type: integer
        format: int32
        description: Identificador del perfil
        example: 1
      nombrePerfil:
        type: string
        description: Nombre del perfil
        example: "Administrador"
      descripcionPerfil:
        type: string
        description: Descripción del perfil
        example: "Perfil con acceso completo al sistema"
      indDefault:
        type: string
        description: Indicador si es el perfil por defecto del usuario
        example: "S"
      multiperfil:
        type: string
        description: Indicador si el usuario tiene múltiples perfiles
        example: "S"
      estado:
        type: string
        description: Estado del perfil del usuario
        example: "ACT"
      indConsulta:
        type: string
        description: Indicador de consulta del perfil
        example: "S"

  PycCategoriaPet:
    type: object
    properties:
      idPeticion:
        type: integer
        format: int32
        description: Identificador de la petición
        example: 1001
      idCategoriaTablero:
        type: integer
        format: int32
        description: Identificador de la categoría del tablero
        example: 5
      nombre:
        type: string
        description: Nombre de la categoría
        example: "Desarrollo"
      total:
        type: integer
        format: int32
        description: Número total de actividades en la categoría
        example: 15
      terminadas:
        type: integer
        format: int32
        description: Número de actividades terminadas
        example: 8
      pendientes:
        type: integer
        format: int32
        description: Número de actividades pendientes
        example: 7
      horasBase:
        type: number
        format: double
        description: Total de horas base planificadas
        example: 120.5
      horasReal:
        type: number
        format: double
        description: Total de horas reales ejecutadas
        example: 135.75
      porcentaje:
        type: number
        format: double
        description: Porcentaje de avance calculado
        example: 85.25
      fechaInicio:
        type: string
        description: Fecha de inicio más temprana de las actividades
        example: "2024-01-15"
      fechaFin:
        type: string
        description: Fecha de fin más tardía de las actividades
        example: "2024-02-20"
      fechaRealTerminado:
        type: string
        description: Fecha real de terminación más tardía de actividades terminadas
        example: "2024-02-18"
      fechaRealPendiente:
        type: string
        description: Fecha de fin más tardía de actividades pendientes
        example: "2024-02-20"

  PycPeticionTab:
    type: object
    properties:
      idPeticion:
        type: integer
        format: int32
        description: Identificador único de la petición
        example: 1001
      nombrePeticion:
        type: string
        description: Nombre descriptivo de la petición
        example: "Desarrollo de nueva funcionalidad"
      descripcionTipoPeticion:
        type: string
        description: Descripción del tipo de petición
        example: "Desarrollo de Software"
      nombreEstado:
        type: string
        description: Nombre del estado actual de la petición
        example: "En Proceso"
      primerNombre:
        type: string
        description: Primer nombre del usuario solicitante
        example: "Juan"

  ValidaEstadoDeducibleResponse:
    type: object
    properties:
      resp:
        type: string
        description: Respuesta de la validación del estado del deducible
        example: "info-Existe más de un deducible a pagar. Comuniniquese con el area de reclamos"

  MontoDeducibleFacResponse:
    type: object
    properties:
      moneda:
        type: string
        description: Código de la moneda
        example: "HNL"
      mtoDeducible:
        type: number
        format: double
        description: Monto del deducible
        example: null
      mtoIva:
        type: number
        format: double
        description: Monto del IVA
        example: null
      mtoCuotas:
        type: number
        format: double
        description: Monto de las cuotas
        example: 0
      mtoTotal:
        type: number
        format: double
        description: Monto total
        example: null

  MontoDeducibleResponse:
    type: object
    properties:
      monto:
        type: number
        format: double
        description: Monto del deducible obtenido
        example: 1500.0